UDS 27服务AES密钥破解 - 2025-06-22 20:00:00.762412
请求CAN ID: 0x715
响应CAN ID: 0x795
测试密钥数量: 26
============================================================

[20:00:00.762] 执行UDS 10 01 (默认会话)...
[20:00:00.762] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:00.763] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:00:00.763] ✅ UDS 10 01 成功
[20:00:01.263] 执行UDS 10 03 (扩展会话)...
[20:00:01.263] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:00:01.265] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:00:01.265] ✅ UDS 10 03 成功
[20:00:01.265] 执行UDS 10 01 (默认会话)...
[20:00:01.265] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:01.266] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:00:01.266] ✅ UDS 10 01 成功
[20:00:01.766] 执行UDS 10 03 (扩展会话)...
[20:00:01.766] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:00:01.767] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:00:01.767] ✅ UDS 10 03 成功
[20:00:02.277] 第  1次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功!
[20:00:02.486] 第  2次: 密钥 2B7E151628AED2A6ABF7158809CF4F3C - ✅ 成功!
[20:00:02.694] 第  3次: 密钥 00000000000000000000000000000000 - ✅ 成功!
[20:00:02.903] 第  4次: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF - ✅ 成功!
[20:00:03.112] 第  5次: 密钥 0123456789ABCDEFFEDCBA9876543210 - ✅ 成功!
[20:00:03.321] 第  6次: 密钥 11111111111111111111111111111111 - ✅ 成功!
[20:00:03.530] 第  7次: 密钥 22222222222222222222222222222222 - ✅ 成功!
[20:00:03.739] 第  8次: 密钥 AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA - ✅ 成功!
[20:00:03.948] 第  9次: 密钥 55555555555555555555555555555555 - ✅ 成功!
[20:00:04.157] 第 10次: 密钥 1234567890ABCDEF1234567890ABCDEF - ✅ 成功!
[20:00:04.366] 第 11次: 密钥 ABCDEF1234567890ABCDEF1234567890 - ✅ 成功!
[20:00:04.575] 第 12次: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 - ✅ 成功!
[20:00:04.784] 第 13次: 密钥 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A - ✅ 成功!
[20:00:04.993] 第 14次: 密钥 20240101000000000000000000000000 - ✅ 成功!
[20:00:05.203] 第 15次: 密钥 20230101000000000000000000000000 - ✅ 成功!
[20:00:05.406] 第 16次: 密钥 BMS1234567890ABCDEF1234567890AB - ❌ 失败
[20:00:05.609] 第 17次: 密钥 ECU1234567890ABCDEF1234567890AB - ❌ 失败
[20:00:05.811] 第 18次: 密钥 CAN1234567890ABCDEF1234567890AB - ❌ 失败
[20:00:06.020] 第 19次: 密钥 DEADBEEFDEADBEEFDEADBEEFDEADBEEF - ✅ 成功!
[20:00:06.229] 第 20次: 密钥 CAFEBABECAFEBABECAFEBABECAFEBABE - ✅ 成功!
[20:00:06.438] 第 21次: 密钥 1122334455667788AABBCCDDEEFF0011 - ✅ 成功!
[20:00:06.648] 第 22次: 密钥 FFEEDDCCBBAA99887766554433221100 - ✅ 成功!
[20:00:06.851] 第 23次: 密钥 0101010101010101010101010101010101 - ❌ 失败
[20:00:07.054] 第 24次: 密钥 1010101010101010101010101010101010 - ❌ 失败
[20:00:07.263] 第 25次: 密钥 ABABABABABABABABABABABABABABABAB - ✅ 成功!
[20:00:07.472] 第 26次: 密钥 CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD - ✅ 成功!
[20:00:07.673] 执行UDS 10 01 (默认会话)...
[20:00:07.673] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.673] 接收: can0  715   [3]  02 27 03
[20:00:07.673] 接收: can0  795   [8]  10 12 67 03 B2 A3 C8 83
[20:00:07.673] ❌ UDS 10 01 失败
[20:00:07.673] 第2轮: UDS 10 01 失败，跳过本轮
[20:00:07.673] 执行UDS 10 01 (默认会话)...
[20:00:07.673] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.673] 接收: can0  715   [3]  30 00 00
[20:00:07.673] 接收: can0  795   [8]  21 BF ED 18 EE 99 4D DC
[20:00:07.673] ❌ UDS 10 01 失败
[20:00:07.673] 第3轮: UDS 10 01 失败，跳过本轮
[20:00:07.673] 执行UDS 10 01 (默认会话)...
[20:00:07.673] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.673] 接收: can0  795   [8]  22 23 B4 CD BB AD 55 55
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第4轮: UDS 10 01 失败，跳过本轮
[20:00:07.674] 执行UDS 10 01 (默认会话)...
[20:00:07.674] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.674] 接收: can0  715   [8]  10 12 27 04 27 EA 5F 8C
[20:00:07.674] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第5轮: UDS 10 01 失败，跳过本轮
[20:00:07.674] 执行UDS 10 01 (默认会话)...
[20:00:07.674] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.674] 接收: can0  715   [8]  21 43 2E 96 99 99 A3 F5
[20:00:07.674] 接收: can0  715   [6]  22 63 DD 90 5A 54
[20:00:07.674] 接收: can0  795   [8]  02 67 04 55 55 55 55 55
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第6轮: UDS 10 01 失败，跳过本轮
[20:00:07.674] 执行UDS 10 01 (默认会话)...
[20:00:07.674] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.674] 接收: can0  715   [3]  02 27 03
[20:00:07.674] 接收: can0  795   [8]  10 12 67 03 42 32 3F 52
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第7轮: UDS 10 01 失败，跳过本轮
[20:00:07.674] 执行UDS 10 01 (默认会话)...
[20:00:07.674] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.674] 接收: can0  715   [3]  30 00 00
[20:00:07.674] 接收: can0  795   [8]  21 29 0E E5 27 3E 2E B4
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第8轮: UDS 10 01 失败，跳过本轮
[20:00:07.674] 执行UDS 10 01 (默认会话)...
[20:00:07.674] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.674] 接收: can0  795   [8]  22 EB E7 89 5D A9 55 55
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第9轮: UDS 10 01 失败，跳过本轮
[20:00:07.674] 执行UDS 10 01 (默认会话)...
[20:00:07.674] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.674] 接收: can0  715   [8]  10 12 27 04 DA 3B 39 4B
[20:00:07.674] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第10轮: UDS 10 01 失败，跳过本轮
[20:00:07.674] 执行UDS 10 01 (默认会话)...
[20:00:07.674] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.674] 接收: can0  715   [8]  21 F6 0B 66 1D 90 9F 00
[20:00:07.674] 接收: can0  715   [6]  22 4D 8E A9 7E B2
[20:00:07.674] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第11轮: UDS 10 01 失败，跳过本轮
[20:00:07.674] 执行UDS 10 01 (默认会话)...
[20:00:07.674] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.674] 接收: can0  715   [3]  02 27 03
[20:00:07.674] 接收: can0  795   [8]  10 12 67 03 30 42 5F E7
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第12轮: UDS 10 01 失败，跳过本轮
[20:00:07.674] 执行UDS 10 01 (默认会话)...
[20:00:07.674] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.674] 接收: can0  715   [3]  30 00 00
[20:00:07.674] 接收: can0  795   [8]  21 4F 25 7C 31 61 7B CE
[20:00:07.674] ❌ UDS 10 01 失败
[20:00:07.674] 第13轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  795   [8]  22 CC 59 87 26 A2 55 55
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第14轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  715   [8]  10 12 27 04 AB DE CD 70
[20:00:07.675] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第15轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  715   [8]  21 16 48 B0 8B D1 AF 95
[20:00:07.675] 接收: can0  715   [6]  22 2B D8 07 2E A2
[20:00:07.675] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第16轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  715   [3]  02 27 03
[20:00:07.675] 接收: can0  795   [8]  10 12 67 03 D1 D5 CE AE
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第17轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  715   [3]  30 00 00
[20:00:07.675] 接收: can0  795   [8]  21 B2 85 78 33 C7 C7 66
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第18轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  795   [8]  22 05 36 09 80 E4 55 55
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第19轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  715   [8]  10 12 27 04 27 3B 51 5A
[20:00:07.675] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第20轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第21轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第22轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第23轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第24轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第25轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  715   [8]  21 D7 6A D1 54 BE 15 9C
[20:00:07.675] 接收: can0  715   [6]  22 89 4E 5C A2 14
[20:00:07.675] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第26轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第27轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第28轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第29轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第30轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第31轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第32轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第33轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第34轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  715   [3]  02 27 03
[20:00:07.675] 接收: can0  795   [8]  10 12 67 03 63 1F C2 B6
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第35轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第36轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第37轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第38轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第39轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第40轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.675] 接收: can0  715   [3]  30 00 00
[20:00:07.675] 接收: can0  795   [8]  21 0D B4 52 9A AF 2F 43
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第41轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.675] ❌ UDS 10 01 失败
[20:00:07.675] 第42轮: UDS 10 01 失败，跳过本轮
[20:00:07.675] 执行UDS 10 01 (默认会话)...
[20:00:07.675] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第43轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第44轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第45轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第46轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第47轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第48轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.676] 接收: can0  795   [8]  22 B9 C7 F5 C4 CB 55 55
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第49轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第50轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第51轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第52轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第53轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第54轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第55轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第56轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.676] 接收: can0  715   [8]  10 12 27 04 18 8C 9C F2
[20:00:07.676] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第57轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第58轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第59轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第60轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第61轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第62轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第63轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第64轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第65轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.676] 接收: can0  715   [8]  21 0F 14 E5 AD 0A 7B B0
[20:00:07.676] 接收: can0  715   [6]  22 0A 56 1C 60 5E
[20:00:07.676] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第66轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第67轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第68轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第69轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第70轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第71轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第72轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.676] 接收: can0  715   [3]  02 27 03
[20:00:07.676] 接收: can0  795   [8]  10 12 67 03 F8 EB BF 1B
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第73轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第74轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第75轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第76轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.676] 发送失败: [Errno 105] No buffer space available
[20:00:07.676] ❌ UDS 10 01 失败
[20:00:07.676] 第77轮: UDS 10 01 失败，跳过本轮
[20:00:07.676] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第78轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第79轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第80轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.677] 接收: can0  715   [3]  30 00 00
[20:00:07.677] 接收: can0  795   [8]  21 CA 01 4B DD 4B 65 09
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第81轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第82轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第83轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第84轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第85轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第86轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第87轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第88轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.677] 接收: can0  795   [8]  22 E7 D2 A3 BD F9 55 55
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第89轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第90轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第91轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第92轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第93轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第94轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第95轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第96轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第97轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:00:07.677] 接收: can0  715   [8]  10 12 27 04 D0 78 D6 C4
[20:00:07.677] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第98轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第99轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 执行UDS 10 01 (默认会话)...
[20:00:07.677] 发送失败: [Errno 105] No buffer space available
[20:00:07.677] ❌ UDS 10 01 失败
[20:00:07.677] 第100轮: UDS 10 01 失败，跳过本轮
[20:00:07.677] 有效密钥: BE11A1C1120344052687183234B9A1A2
[20:00:07.677] 有效密钥: 2B7E151628AED2A6ABF7158809CF4F3C
[20:00:07.677] 有效密钥: 00000000000000000000000000000000
[20:00:07.677] 有效密钥: FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[20:00:07.677] 有效密钥: 0123456789ABCDEFFEDCBA9876543210
[20:00:07.677] 有效密钥: 11111111111111111111111111111111
[20:00:07.677] 有效密钥: 22222222222222222222222222222222
[20:00:07.677] 有效密钥: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
[20:00:07.677] 有效密钥: 55555555555555555555555555555555
[20:00:07.677] 有效密钥: 1234567890ABCDEF1234567890ABCDEF
[20:00:07.677] 有效密钥: ABCDEF1234567890ABCDEF1234567890
[20:00:07.677] 有效密钥: A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5
[20:00:07.677] 有效密钥: 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A
[20:00:07.677] 有效密钥: 20240101000000000000000000000000
[20:00:07.677] 有效密钥: 20230101000000000000000000000000
[20:00:07.677] 有效密钥: DEADBEEFDEADBEEFDEADBEEFDEADBEEF
[20:00:07.677] 有效密钥: CAFEBABECAFEBABECAFEBABECAFEBABE
[20:00:07.677] 有效密钥: 1122334455667788AABBCCDDEEFF0011
[20:00:07.677] 有效密钥: FFEEDDCCBBAA99887766554433221100
[20:00:07.677] 有效密钥: ABABABABABABABABABABABABABABABAB
[20:00:07.677] 有效密钥: CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD
[20:00:07.677] 
破解结果统计:
[20:00:07.677] 总测试密钥数: 26
[20:00:07.677] 成功密钥数: 21
[20:00:07.677] 失败密钥数: 5
[20:00:07.677] 成功率: 80.77%
