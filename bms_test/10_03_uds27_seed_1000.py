#!/usr/bin/env python3
"""
UDS 27服务随机性测试工具
1. 先执行UDS 10 01 (默认会话)
2. 再执行UDS 10 03 (扩展会话)
3. 循环调用uds_27_advanced 1000次
4. 检测seed和key的重复性
"""

import socket
import struct
import time
import sys
import subprocess
import re
import os
from datetime import datetime
from collections import defaultdict

# CAN帧格式常量
CAN_RAW = 1

class CANFrame:
    """CAN帧类"""
    def __init__(self, can_id=0, data=b''):
        self.can_id = can_id
        self.data = data
        self.dlc = len(data)
    
    def pack(self):
        """打包CAN帧为socket发送格式"""
        fmt = "=IB3x8s"
        return struct.pack(fmt, self.can_id, self.dlc, self.data.ljust(8, b'\x00'))
    
    @classmethod
    def unpack(cls, data):
        """从socket接收数据解包CAN帧"""
        fmt = "=IB3x8s"
        can_id, dlc, frame_data = struct.unpack(fmt, data)
        return cls(can_id, frame_data[:dlc])
    
    def __str__(self):
        """格式化输出CAN帧"""
        data_str = ' '.join(f'{b:02X}' for b in self.data)
        return f"can0  {self.can_id:03X}   [{self.dlc}]  {data_str}"

class UDS27Tester:
    """UDS 27服务测试器"""

    def __init__(self, can_id, response_id=None, interface='can0'):
        self.can_id = int(can_id, 16) if isinstance(can_id, str) else can_id
        self.interface = interface
        self.socket = None
        self.log_file = None

        # 设置响应CAN ID
        if response_id is not None:
            self.response_id = int(response_id, 16) if isinstance(response_id, str) else response_id
        else:
            # 默认计算响应CAN ID (通常是请求ID + 8)
            self.response_id = self.can_id + 8
        
        # 存储seed和key的记录
        self.seed_records = []
        self.key_records = []
        self.seed_count = defaultdict(int)
        self.key_count = defaultdict(int)
        
        # UDS_27_advanced程序路径
        self.uds27_path = os.path.join("uds27", "uds_27_advanced")
        if not os.path.exists(self.uds27_path):
            self.uds27_path = "./uds_27_advanced"  # 备用路径
    
    def init_can_socket(self):
        """初始化CAN socket"""
        try:
            self.socket = socket.socket(socket.PF_CAN, socket.SOCK_RAW, CAN_RAW)
            self.socket.bind((self.interface,))
            self.socket.settimeout(1.0)
            print(f"CAN接口 {self.interface} 初始化成功")
            return True
        except Exception as e:
            print(f"初始化CAN接口失败: {e}")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        import os

        # 确保logs目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"10_03_uds27_seed_1000_{self.can_id:03X}_{timestamp}.log")

        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"UDS 27服务随机性测试 - {datetime.now()}\n")
            self.log_file.write(f"CAN ID: 0x{self.can_id:03X}\n")
            self.log_file.write("=" * 60 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_can_frame(self, frame):
        """发送CAN帧"""
        try:
            self.socket.send(frame.pack())
            self.log_message(f"发送: {frame}", False)
            return True
        except Exception as e:
            self.log_message(f"发送失败: {e}")
            return False
    
    def receive_can_frame(self, timeout=1.0):
        """接收CAN帧"""
        try:
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(timeout)
            
            data = self.socket.recv(16)
            frame = CANFrame.unpack(data)
            
            self.socket.settimeout(original_timeout)
            self.log_message(f"接收: {frame}", False)
            return frame
            
        except socket.timeout:
            return None
        except Exception as e:
            self.log_message(f"接收失败: {e}")
            return None
    
    def send_uds_request(self, service_data):
        """发送UDS请求并等待响应"""
        # 填充到8字节
        padded_data = service_data + b'\x55' * (8 - len(service_data))
        request_frame = CANFrame(self.can_id, padded_data)
        
        if not self.send_can_frame(request_frame):
            return None
        
        # 等待响应
        start_time = time.time()
        while time.time() - start_time < 2.0:
            response = self.receive_can_frame(0.5)
            if response and response.can_id == self.response_id:
                return response
        
        return None
    
    def execute_uds_10_01(self):
        """执行UDS 10 01 (默认会话)"""
        print("执行UDS 10 01 (默认会话)...")
        response = self.send_uds_request(b'\x02\x10\x01')
        
        if response and len(response.data) >= 2 and response.data[1] == 0x50:
            self.log_message("✅ UDS 10 01 成功")
            return True
        else:
            self.log_message("❌ UDS 10 01 失败")
            return False
    
    def execute_uds_10_03(self):
        """执行UDS 10 03 (扩展会话)"""
        print("执行UDS 10 03 (扩展会话)...")
        response = self.send_uds_request(b'\x02\x10\x03')
        
        if response and len(response.data) >= 2 and response.data[1] == 0x50:
            self.log_message("✅ UDS 10 03 成功")
            return True
        else:
            self.log_message("❌ UDS 10 03 失败")
            return False
    
    def call_uds27_advanced(self, iteration):
        """调用uds_27_advanced程序"""
        try:
            # 构造命令
            cmd = [self.uds27_path, "-r", f"0x{self.can_id:X}", "-v"]
            
            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            output = result.stdout + result.stderr
            
            # 解析seed和key
            seed_match = re.search(r'输入Seed:\s*([0-9A-F\s]+)', output)
            key_match = re.search(r'发送Key:\s*([0-9A-F\s]+)', output)
            
            seed = None
            key = None
            
            if seed_match:
                seed_str = seed_match.group(1).replace(' ', '')
                seed = seed_str
                
            if key_match:
                key_str = key_match.group(1).replace(' ', '').split('(')[0]  # 移除注释部分
                key = key_str
            
            if seed and key:
                self.log_message(f"第{iteration:4d}次: Seed={seed}, Key={key}", False)
                return seed, key
            else:
                self.log_message(f"第{iteration:4d}次: 解析失败", False)
                self.log_message(f"输出: {output[:200]}...", False)
                return None, None
                
        except subprocess.TimeoutExpired:
            self.log_message(f"第{iteration:4d}次: 超时")
            return None, None
        except Exception as e:
            self.log_message(f"第{iteration:4d}次: 错误 - {e}")
            return None, None
    
    def run_uds27_test(self, iterations=1000):
        """运行UDS 27测试"""
        print(f"\n开始UDS 27随机性测试 (共{iterations}次)...")
        print("=" * 50)
        
        success_count = 0
        
        for i in range(1, iterations + 1):
            if i % 50 == 0:
                print(f"进度: {i}/{iterations} ({i/iterations*100:.1f}%)")
            
            seed, key = self.call_uds27_advanced(i)
            
            if seed and key:
                self.seed_records.append(seed)
                self.key_records.append(key)
                self.seed_count[seed] += 1
                self.key_count[key] += 1
                success_count += 1
            
            # 短暂延时
            time.sleep(0.1)
        
        print(f"\n测试完成: {success_count}/{iterations} 次成功")
        return success_count
    
    def analyze_results(self):
        """分析结果"""
        print("\n" + "=" * 50)
        print("随机性分析结果")
        print("=" * 50)
        
        total_seeds = len(self.seed_records)
        unique_seeds = len(set(self.seed_records))
        total_keys = len(self.key_records)
        unique_keys = len(set(self.key_records))
        
        print(f"总测试次数: {total_seeds}")
        print(f"唯一Seed数: {unique_seeds}")
        print(f"唯一Key数:  {unique_keys}")
        print(f"Seed重复率: {(total_seeds - unique_seeds) / total_seeds * 100:.2f}%")
        print(f"Key重复率:  {(total_keys - unique_keys) / total_keys * 100:.2f}%")
        
        # 检查重复的seed
        duplicate_seeds = {seed: count for seed, count in self.seed_count.items() if count > 1}
        duplicate_keys = {key: count for key, count in self.key_count.items() if count > 1}
        
        if duplicate_seeds:
            print(f"\n❌ 发现重复的Seed ({len(duplicate_seeds)}个):")
            for seed, count in sorted(duplicate_seeds.items(), key=lambda x: x[1], reverse=True):
                print(f"  {seed}: 出现{count}次")
                self.log_message(f"重复Seed: {seed} (出现{count}次)", False)
        
        if duplicate_keys:
            print(f"\n❌ 发现重复的Key ({len(duplicate_keys)}个):")
            for key, count in sorted(duplicate_keys.items(), key=lambda x: x[1], reverse=True):
                print(f"  {key}: 出现{count}次")
                self.log_message(f"重复Key: {key} (出现{count}次)", False)
        
        if not duplicate_seeds and not duplicate_keys:
            print("\n✅ 测试通过: 未发现重复的Seed或Key")
            self.log_message("✅ 随机性测试通过", False)
        else:
            print(f"\n❌ 测试失败: 发现{len(duplicate_seeds)}个重复Seed，{len(duplicate_keys)}个重复Key")
            self.log_message("❌ 随机性测试失败", False)
        
        # 记录统计信息到日志
        self.log_message(f"\n统计信息:", False)
        self.log_message(f"总测试次数: {total_seeds}", False)
        self.log_message(f"唯一Seed数: {unique_seeds}", False)
        self.log_message(f"唯一Key数: {unique_keys}", False)
        self.log_message(f"Seed重复率: {(total_seeds - unique_seeds) / total_seeds * 100:.2f}%", False)
        self.log_message(f"Key重复率: {(total_keys - unique_keys) / total_keys * 100:.2f}%", False)
        
        return len(duplicate_seeds) == 0 and len(duplicate_keys) == 0
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行完整测试"""
        print("UDS 27服务随机性测试工具")
        print("=" * 30)
        print(f"目标CAN ID: 0x{self.can_id:03X}")
        print(f"响应CAN ID: 0x{self.response_id:03X}")
        print(f"CAN接口: {self.interface}")
        
        # 检查uds_27_advanced程序
        if not os.path.exists(self.uds27_path):
            print(f"❌ 找不到uds_27_advanced程序: {self.uds27_path}")
            return False
        
        # 初始化
        if not self.init_can_socket():
            return False
        
        if not self.open_log_file():
            return False
        
        try:
            # 执行UDS 10 01
            if not self.execute_uds_10_01():
                return False
            
            time.sleep(0.5)
            
            # 执行UDS 10 03
            if not self.execute_uds_10_03():
                return False
            
            time.sleep(0.5)
            
            # 运行UDS 27测试
            success_count = self.run_uds27_test(1000)
            
            if success_count < 100:  # 至少需要100次成功
                print(f"❌ 成功次数太少: {success_count}/1000")
                return False
            
            # 分析结果
            return self.analyze_results()
            
        except KeyboardInterrupt:
            print("\n用户中断测试")
            return False
        except Exception as e:
            print(f"\n测试过程中发生错误: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 10_03_uds27_seed_1000.py <请求CAN_ID> [响应CAN_ID] [接口]")
        print("示例: python3 10_03_uds27_seed_1000.py 0x715")
        print("      python3 10_03_uds27_seed_1000.py 0x715 0x795")
        print("      python3 10_03_uds27_seed_1000.py 715 795 can0")
        print("说明:")
        print("  请求CAN_ID: 必需，发送UDS请求的CAN ID")
        print("  响应CAN_ID: 可选，接收UDS响应的CAN ID (默认为请求ID+8)")
        print("  接口:       可选，CAN接口名称 (默认为can0)")
        sys.exit(1)

    can_id_str = sys.argv[1]

    # 解析请求CAN ID
    try:
        if can_id_str.startswith('0x') or can_id_str.startswith('0X'):
            can_id = int(can_id_str, 16)
        else:
            can_id = int(can_id_str, 16)
    except ValueError:
        print(f"❌ 无效的请求CAN ID: {can_id_str}")
        sys.exit(1)

    # 解析响应CAN ID
    response_id = None
    if len(sys.argv) > 2:
        response_id_str = sys.argv[2]
        try:
            if response_id_str.startswith('0x') or response_id_str.startswith('0X'):
                response_id = int(response_id_str, 16)
            else:
                response_id = int(response_id_str, 16)
        except ValueError:
            print(f"❌ 无效的响应CAN ID: {response_id_str}")
            sys.exit(1)

    # 获取接口名称
    interface = sys.argv[3] if len(sys.argv) > 3 else 'can0'

    tester = UDS27Tester(can_id, response_id, interface)
    
    try:
        success = tester.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
