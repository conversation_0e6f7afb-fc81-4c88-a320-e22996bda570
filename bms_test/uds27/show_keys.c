#include <stdio.h>
#include <stdint.h>
#include "aes_keys.h"

int main() {
    printf("UDS 27服务 - AES密钥信息\n");
    printf("========================\n\n");
    
    printf("可用的AES密钥类型:\n\n");
    
    // 显示所有密钥类型
    for (int i = 0; i <= 4; i++) {
        aes_key_type_t key_type = (aes_key_type_t)i;
        const uint8_t* key = get_aes_key_by_type(key_type);
        
        printf("类型 %d: %s\n", i, get_aes_key_description(key_type));
        printf("密钥: ");
        for (int j = 0; j < 16; j++) {
            printf("%02X", key[j]);
            if (j == 7) printf(" ");
        }
        printf("\n");
        
        // 特别说明NIST密钥
        if (key_type == AES_KEY_TYPE_NIST) {
            printf("说明: 这是您选中的NIST标准测试向量密钥\n");
            printf("      广泛用于AES算法验证，但不应在生产环境使用\n");
        }
        printf("\n");
    }
    
    printf("使用方法:\n");
    printf("========\n");
    printf("./uds_27_advanced -k 0  # 使用NIST标准密钥 (您选中的)\n");
    printf("./uds_27_advanced -k 1  # 使用全零密钥\n");
    printf("./uds_27_advanced -k 2  # 使用简单密钥\n");
    printf("./uds_27_advanced -k 3  # 使用BMS模式1\n");
    printf("./uds_27_advanced -k 4  # 使用BMS模式2\n");
    printf("./uds_27_advanced -c 2B7E151628AED2A6ABF7158809CF4F3C  # 自定义密钥\n");
    printf("\n");
    
    printf("注意事项:\n");
    printf("========\n");
    printf("1. 默认使用NIST标准密钥 (类型0)\n");
    printf("2. 生产环境应使用厂商提供的专用密钥\n");
    printf("3. 自定义密钥必须是32位十六进制字符串\n");
    printf("4. 模拟器和客户端必须使用相同密钥才能验证成功\n");
    
    return 0;
}
