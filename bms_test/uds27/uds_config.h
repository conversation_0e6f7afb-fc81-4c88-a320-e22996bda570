#ifndef UDS_CONFIG_H
#define UDS_CONFIG_H

#include <stdint.h>
#include "aes_keys.h"

// CAN配置
#define CAN_INTERFACE "can0"
#define UDS_REQUEST_ID_DEFAULT 0x715    // 默认请求ID (物理地址)
#define UDS_REQUEST_ID_ALT 0x7DF        // 替代请求ID (功能地址)
#define UDS_RESPONSE_ID 0x795           // 响应ID

// UDS服务配置
#define UDS_SERVICE_27 0x27
#define UDS_SUBFUNCTION_REQUEST_SEED 0x03
#define UDS_SUBFUNCTION_SEND_KEY 0x04

// 超时配置 (毫秒)
#define UDS_TIMEOUT_MS 1000
#define FLOW_CONTROL_TIMEOUT_MS 500

// AES密钥配置 - 使用aes_keys.h中定义的密钥

// 配置结构体
typedef struct {
    uint32_t request_id;
    uint32_t response_id;
    aes_key_type_t key_type;
    uint8_t custom_key[16];
    int timeout_ms;
} uds_config_t;

// 默认配置
static const uds_config_t DEFAULT_CONFIG = {
    .request_id = UDS_REQUEST_ID_DEFAULT,
    .response_id = UDS_RESPONSE_ID,
    .key_type = AES_KEY_TYPE_REAL_BMS,
    .custom_key = {0},
    .timeout_ms = UDS_TIMEOUT_MS
};

// 获取指定类型的AES密钥
static inline const uint8_t* get_aes_key(aes_key_type_t key_type, const uint8_t* custom_key) {
    if (key_type == AES_KEY_TYPE_CUSTOM) {
        return custom_key;
    }
    return get_aes_key_by_type(key_type);
}

#endif // UDS_CONFIG_H
