CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2
INCLUDES = -I.
SOURCES_BASIC = uds_27_service.c aes/aes.c
SOURCES_ADVANCED = uds_27_advanced.c aes/aes.c
SOURCES_SIMULATOR = can_simulator.c aes/aes.c
SOURCES_SHOWKEYS = show_keys.c
SOURCES_VERIFY = verify_real_data.c aes/aes.c
SOURCES_ANALYZE = analyze_algorithm.c aes/aes.c
SOURCES_FINDTRANS = find_transform.c aes/aes.c
SOURCES_FINAL = final_verification.c aes/aes.c
SOURCES_CALCKEY = calculate_key.c aes/aes.c
SOURCES_TESTLOGIC = test_corrected_logic.c aes/aes.c
TARGET_BASIC = uds_27_service
TARGET_ADVANCED = uds_27_advanced
TARGET_SIMULATOR = can_simulator
TARGET_SHOWKEYS = show_keys
TARGET_VERIFY = verify_real_data
TARGET_ANALYZE = analyze_algorithm
TARGET_FINDTRANS = find_transform
TARGET_FINAL = final_verification
TARGET_CALCKEY = calculate_key
TARGET_TESTLOGIC = test_corrected_logic

.PHONY: all clean basic advanced simulator showkeys verify analyze findtrans final calckey testlogic

all: basic advanced simulator showkeys verify analyze findtrans final calckey testlogic

basic: $(TARGET_BASIC)

advanced: $(TARGET_ADVANCED)

simulator: $(TARGET_SIMULATOR)

showkeys: $(TARGET_SHOWKEYS)

verify: $(TARGET_VERIFY)

analyze: $(TARGET_ANALYZE)

findtrans: $(TARGET_FINDTRANS)

final: $(TARGET_FINAL)

calckey: $(TARGET_CALCKEY)

testlogic: $(TARGET_TESTLOGIC)

$(TARGET_BASIC): $(SOURCES_BASIC)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_BASIC) $(SOURCES_BASIC)

$(TARGET_ADVANCED): $(SOURCES_ADVANCED)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_ADVANCED) $(SOURCES_ADVANCED)

$(TARGET_SIMULATOR): $(SOURCES_SIMULATOR)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_SIMULATOR) $(SOURCES_SIMULATOR)

$(TARGET_SHOWKEYS): $(SOURCES_SHOWKEYS)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_SHOWKEYS) $(SOURCES_SHOWKEYS)

$(TARGET_VERIFY): $(SOURCES_VERIFY)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_VERIFY) $(SOURCES_VERIFY)

$(TARGET_ANALYZE): $(SOURCES_ANALYZE)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_ANALYZE) $(SOURCES_ANALYZE)

$(TARGET_FINDTRANS): $(SOURCES_FINDTRANS)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_FINDTRANS) $(SOURCES_FINDTRANS)

$(TARGET_FINAL): $(SOURCES_FINAL)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_FINAL) $(SOURCES_FINAL)

$(TARGET_CALCKEY): $(SOURCES_CALCKEY)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_CALCKEY) $(SOURCES_CALCKEY)

$(TARGET_TESTLOGIC): $(SOURCES_TESTLOGIC)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET_TESTLOGIC) $(SOURCES_TESTLOGIC)

clean:
	rm -f $(TARGET_BASIC) $(TARGET_ADVANCED) $(TARGET_SIMULATOR) $(TARGET_SHOWKEYS) $(TARGET_VERIFY) $(TARGET_ANALYZE) $(TARGET_FINDTRANS) $(TARGET_FINAL) $(TARGET_CALCKEY) $(TARGET_TESTLOGIC)

install_deps:
	sudo apt-get update
	sudo apt-get install -y can-utils

setup_can:
	sudo modprobe can
	sudo modprobe can_raw
	sudo modprobe vcan
	sudo ip link add dev vcan0 type vcan
	sudo ip link set up vcan0

test_virtual:
	# 在虚拟CAN接口上测试
	candump vcan0 &
	cansend vcan0 7DF#0227030000000000

help:
	@echo "可用的目标:"
	@echo "  all         - 编译程序"
	@echo "  clean       - 清理编译文件"
	@echo "  install_deps- 安装依赖包"
	@echo "  setup_can   - 设置虚拟CAN接口"
	@echo "  test_virtual- 在虚拟CAN上测试"
	@echo "  help        - 显示此帮助信息"
