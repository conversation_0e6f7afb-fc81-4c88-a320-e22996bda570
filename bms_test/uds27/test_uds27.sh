#!/bin/bash

# UDS 27服务测试脚本
# 用于测试不同的AES密钥和配置

echo "UDS 27服务测试脚本"
echo "=================="

# 检查是否已编译程序
if [ ! -f "uds_27_advanced" ]; then
    echo "正在编译程序..."
    make advanced
    if [ $? -ne 0 ]; then
        echo "编译失败!"
        exit 1
    fi
fi

# 检查CAN接口是否存在
if ! ip link show can0 > /dev/null 2>&1; then
    echo "错误: CAN接口 can0 不存在"
    echo "请先运行以下命令设置CAN接口:"
    echo "  sudo ./init_can0.sh"
    echo ""
    echo "或者设置虚拟CAN接口进行测试:"
    echo "  sudo modprobe vcan"
    echo "  sudo ip link add dev vcan0 type vcan"
    echo "  sudo ip link set up vcan0"
    echo "  # 然后使用 -i vcan0 参数"
    exit 1
fi

echo "CAN接口状态:"
ip -details link show can0

echo ""
echo "可用的测试选项:"
echo "1. 使用默认配置 (0x715 -> 0x795, 默认AES密钥)"
echo "2. 使用功能地址 (0x7DF -> 0x795, 默认AES密钥)"
echo "3. 使用全零AES密钥"
echo "4. 使用简单AES密钥"
echo "5. 使用自定义AES密钥"
echo "6. 详细模式测试"
echo "7. 显示帮助信息"
echo "8. 退出"

while true; do
    echo ""
    read -p "请选择测试选项 (1-8): " choice
    
    case $choice in
        1)
            echo "执行默认配置测试..."
            ./uds_27_advanced
            ;;
        2)
            echo "执行功能地址测试..."
            ./uds_27_advanced -r 0x7DF
            ;;
        3)
            echo "执行全零密钥测试..."
            ./uds_27_advanced -k 1 -v
            ;;
        4)
            echo "执行简单密钥测试..."
            ./uds_27_advanced -k 2 -v
            ;;
        5)
            echo "请输入32位十六进制AES密钥 (例如: 0123456789ABCDEFFEDCBA9876543210):"
            read -p "AES密钥: " custom_key
            if [ ${#custom_key} -eq 32 ]; then
                echo "执行自定义密钥测试..."
                ./uds_27_advanced -c $custom_key -v
            else
                echo "错误: 密钥长度必须是32个十六进制字符"
            fi
            ;;
        6)
            echo "执行详细模式测试..."
            ./uds_27_advanced -v
            ;;
        7)
            echo "显示帮助信息..."
            ./uds_27_advanced -h
            ;;
        8)
            echo "退出测试"
            break
            ;;
        *)
            echo "无效选择，请输入1-8"
            ;;
    esac
done

echo "测试完成"
