# UDS 27 04 Key帧发送修正

## 问题描述

在UDS 27 04服务的多帧传输中发现key数据有偏差：

### 修正前的问题
```
计算的Key: AF 1C FA 7A 3D EA 98 3B B5 56 37 CD EA E6 38 05
实际发送:   00 AF 1C FA 7A 3D EA 98 3B B5 56 37 CD EA E6 38
           ↑                                              ↑
        多了00                                        少了05
```

### 问题原因
在 `send_key` 函数中：
1. 第一帧被初始化为 `{0x10, 0x12, 0x27, 0x04, 0x00}`，其中 `0x00` 占用了key的位置
2. 后续代码从 `first_frame[5]` 开始填充key，导致整体偏移
3. 最后一个字节 `0x05` 被丢失

## 修正方案

### 修正前的代码
```c
uint8_t first_frame[8] = {0x10, 0x12, UDS_SERVICE_27, UDS_SUBFUNCTION_SEND_KEY, 0x00};

// 复制key的前3个字节到第一帧
first_frame[5] = key[0];
first_frame[6] = key[1];
first_frame[7] = key[2];

// 复制key的中间7个字节到第二帧
for (int i = 0; i < 7; i++) {
    second_frame[1 + i] = key[3 + i];
}

// 复制key的最后6个字节到第三帧
for (int i = 0; i < 6; i++) {
    third_frame[1 + i] = key[10 + i];
}
```

### 修正后的代码
```c
uint8_t first_frame[8] = {0x10, 0x12, UDS_SERVICE_27, UDS_SUBFUNCTION_SEND_KEY};

// 复制key的前4个字节到第一帧 (从first_frame[4]开始)
first_frame[4] = key[0];
first_frame[5] = key[1];
first_frame[6] = key[2];
first_frame[7] = key[3];

// 复制key的中间7个字节到第二帧
for (int i = 0; i < 7; i++) {
    second_frame[1 + i] = key[4 + i];
}

// 复制key的最后5个字节到第三帧
for (int i = 0; i < 5; i++) {
    third_frame[1 + i] = key[11 + i];
}
```

## 修正结果

### 修正后的帧结构
```
第一帧: 10 12 27 04 AF 1C FA 7A  (包含key的前4字节)
第二帧: 21 3D EA 98 3B B5 56 37  (包含key的中7字节)
第三帧: 22 CD EA E6 38 05        (包含key的后5字节)
```

### Key数据分布
- **第一帧**: 4字节头部 + 4字节key数据 = 8字节
- **第二帧**: 1字节序列号 + 7字节key数据 = 8字节  
- **第三帧**: 1字节序列号 + 5字节key数据 = 6字节
- **总计**: 4 + 7 + 5 = 16字节key数据 ✅

## 验证

### 测试程序
使用 `test_key_frames.c` 验证帧构造的正确性：

```bash
./test_key_frames
```

### 验证结果
```
原始Key: AF 1C FA 7A 3D EA 98 3B B5 56 37 CD EA E6 38 05 
重新组装的Key: AF 1C FA 7A 3D EA 98 3B B5 56 37 CD EA E6 38 05 
验证结果: ✅ 匹配
```

## 影响的文件

1. **uds_27_advanced.c** - 高级版本客户端
2. **uds_27_service.c** - 基础版本客户端

## 测试建议

修正后的程序现在会正确发送完整的16字节key数据，应该能够被BMS正确接收和验证。

### 期望的通信流程
```
发送: can0  715   [8]  10 12 27 04 AF 1C FA 7A
接收: can0  795   [8]  30 08 00 55 55 55 55 55
发送: can0  715   [8]  21 3D EA 98 3B B5 56 37
发送: can0  715   [6]  22 CD EA E6 38 05
接收: can0  795   [8]  02 67 04 55 55 55 55 55  ← 期望成功响应
```

## 总结

✅ **问题已修正**: UDS 27 04现在会发送完整的16字节key数据
✅ **验证通过**: 帧构造和key重组测试成功
✅ **兼容性**: 修正适用于基础版本和高级版本程序
