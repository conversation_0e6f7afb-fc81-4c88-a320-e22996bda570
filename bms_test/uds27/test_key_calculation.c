#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes/aes.h"
#include "aes_keys.h"

// 模拟修正后的key计算函数
void calculate_key_for_sending(const uint8_t *seed, uint8_t *key, const uint8_t *aes_key) {
    struct AES_ctx ctx;
    
    // 初始化AES上下文
    AES_init_ctx(&ctx, aes_key);
    
    // 复制seed到key缓冲区
    memcpy(key, seed, 16);
    
    // 使用标准AES ECB模式加密 (不进行BMS后处理)
    AES_ECB_encrypt(&ctx, key);
}

int main() {
    printf("修正后的Key计算测试\n");
    printf("==================\n\n");
    
    // 测试用的seed
    uint8_t test_seed[16] = {
        0xD3, 0x21, 0xB2, 0x96, 0x69, 0xA9, 0x43, 0xF6,
        0x93, 0xD1, 0x5E, 0x40, 0x82, 0xA9, 0x3F, 0xEA
    };
    
    // 真实BMS的AES密钥
    const uint8_t *aes_key = get_aes_key_by_type(AES_KEY_TYPE_REAL_BMS);
    
    // 计算要发送的key
    uint8_t key_to_send[16];
    calculate_key_for_sending(test_seed, key_to_send, aes_key);
    
    printf("输入Seed: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", test_seed[i]);
    }
    printf("\n\n");
    
    printf("AES密钥:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", aes_key[i]);
    }
    printf("\n\n");
    
    printf("要发送的Key: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", key_to_send[i]);
    }
    printf("\n\n");
    
    printf("这个Key将在UDS 27 04服务中发送给BMS\n");
    printf("BMS收到后会内部进行后处理验证\n\n");
    
    // 验证与真实通信记录的匹配
    uint8_t expected_sent_key[16] = {
        0xCF, 0x43, 0xFF, 0x5E, 0x2A, 0x8C, 0x26, 0x44,
        0xC9, 0x40, 0x45, 0x05, 0x5A, 0x24, 0xEB, 0xBA
    };
    
    printf("验证结果:\n");
    printf("========\n");
    printf("期望发送: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", expected_sent_key[i]);
    }
    printf("\n");
    
    printf("实际计算: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X", key_to_send[i]);
    }
    printf("\n");
    
    int match = memcmp(key_to_send, expected_sent_key, 16) == 0;
    printf("结果: %s\n", match ? "✅ 匹配!" : "❌ 不匹配");
    
    if (match) {
        printf("\n✅ 修正成功! 现在程序会发送正确的key\n");
    }
    
    return match ? 0 : 1;
}
