#!/usr/bin/env python3
"""
UDS刷写工具 - 基于UDS-刷写报文.csv
实现三个block的刷写过程
"""

import socket
import struct
import time
import sys
import os
import subprocess
from datetime import datetime

# CAN帧格式常量
CAN_RAW = 1

class CANFrame:
    """CAN帧类"""
    def __init__(self, can_id=0, data=b''):
        self.can_id = can_id
        self.data = data
        self.dlc = len(data)
    
    def pack(self):
        """打包CAN帧为socket发送格式"""
        fmt = "=IB3x8s"
        return struct.pack(fmt, self.can_id, self.dlc, self.data.ljust(8, b'\x00'))
    
    @classmethod
    def unpack(cls, data):
        """从socket接收数据解包CAN帧"""
        fmt = "=IB3x8s"
        can_id, dlc, frame_data = struct.unpack(fmt, data)
        return cls(can_id, frame_data[:dlc])
    
    def __str__(self):
        """格式化输出CAN帧"""
        data_str = ' '.join(f'{b:02X}' for b in self.data)
        return f"can0  {self.can_id:03X}   [{self.dlc}]  {data_str}"

class UDSFlasher:
    """UDS刷写器"""
    
    def __init__(self, request_id=0x715, response_id=0x795, interface='can0'):
        self.request_id = request_id
        self.response_id = response_id
        self.interface = interface
        
        self.socket = None
        self.log_file = None
        
        # 从CSV文件提取的关键数据
        self.seed = None
        self.key = None
        
        # 刷写状态
        self.current_block = 0
        self.blocks_completed = 0
    
    def init_can_socket(self):
        """初始化CAN socket"""
        try:
            self.socket = socket.socket(socket.PF_CAN, socket.SOCK_RAW, CAN_RAW)
            self.socket.bind((self.interface,))
            self.socket.settimeout(2.0)
            print(f"CAN接口 {self.interface} 初始化成功")
            return True
        except Exception as e:
            print(f"初始化CAN接口失败: {e}")
            return False
    
    def open_log_file(self):
        """打开日志文件"""
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(logs_dir, f"hex_uds_flash_{timestamp}.log")
        
        try:
            self.log_file = open(log_filename, 'w')
            self.log_file.write(f"UDS刷写日志 - {datetime.now()}\n")
            self.log_file.write(f"请求CAN ID: 0x{self.request_id:03X}\n")
            self.log_file.write(f"响应CAN ID: 0x{self.response_id:03X}\n")
            self.log_file.write("=" * 60 + "\n\n")
            print(f"日志文件: {log_filename}")
            return True
        except Exception as e:
            print(f"创建日志文件失败: {e}")
            return False
    
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(message)
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
    
    def send_can_frame(self, frame):
        """发送CAN帧"""
        try:
            self.socket.send(frame.pack())
            self.log_message(f"发送: {frame}", False)
            return True
        except Exception as e:
            self.log_message(f"发送失败: {e}")
            return False
    
    def receive_can_frame(self, timeout=2.0):
        """接收CAN帧（只接收来自指定响应ID的帧）"""
        try:
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(0.1)
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    data = self.socket.recv(16)
                    frame = CANFrame.unpack(data)
                    
                    self.log_message(f"接收到帧: {frame}", False)
                    
                    if frame.can_id == self.response_id:
                        self.socket.settimeout(original_timeout)
                        self.log_message(f"接收目标响应: {frame}", False)
                        return frame
                    else:
                        self.log_message(f"忽略非目标ID帧: 0x{frame.can_id:03X}", False)
                        
                except socket.timeout:
                    continue
                except Exception as e:
                    self.socket.settimeout(original_timeout)
                    self.log_message(f"接收失败: {e}", False)
                    return None
            
            self.socket.settimeout(original_timeout)
            self.log_message(f"等待响应ID 0x{self.response_id:03X} 超时", False)
            return None
            
        except Exception as e:
            self.log_message(f"接收过程错误: {e}", False)
            return None
    
    def send_uds_request(self, service_data):
        """发送UDS请求并等待响应"""
        padded_data = service_data + b'\x55' * (8 - len(service_data))
        request_frame = CANFrame(self.request_id, padded_data)
        
        if not self.send_can_frame(request_frame):
            return None
        
        return self.receive_can_frame(3.0)
    
    def step_1_session_control(self):
        """步骤1: 诊断会话控制"""
        self.log_message("=== 步骤1: 诊断会话控制 ===")

        # 先尝试UDS 10 01 (默认会话)
        self.log_message("发送UDS 10 01 (默认会话)...")
        response = self.send_uds_request(b'\x02\x10\x01')

        if response and len(response.data) >= 2 and response.data[1] == 0x50:
            self.log_message("✅ UDS 10 01 成功")
        else:
            self.log_message("⚠️ UDS 10 01 失败，继续尝试扩展会话")

        time.sleep(0.5)

        # UDS 10 03 (扩展会话)
        self.log_message("发送UDS 10 03 (扩展会话)...")
        response = self.send_uds_request(b'\x02\x10\x03')

        if response and len(response.data) >= 2 and response.data[1] == 0x50:
            self.log_message("✅ UDS 10 03 成功")
            return True
        else:
            self.log_message("❌ UDS 10 03 失败")
            return False
    
    def receive_multiframe_response(self):
        """接收多帧响应"""
        frames = []

        # 接收首帧
        first_frame = self.receive_can_frame(3.0)
        if not first_frame or first_frame.data[0] & 0xF0 != 0x10:
            return None

        frames.append(first_frame)
        total_length = ((first_frame.data[0] & 0x0F) << 8) | first_frame.data[1]

        # 发送流控帧
        flow_control = b'\x30\x00\x00\x55\x55\x55\x55\x55'
        fc_frame = CANFrame(self.request_id, flow_control)
        self.send_can_frame(fc_frame)

        # 接收连续帧
        expected_seq = 1
        while len(frames) == 1 or sum(len(f.data) - 2 if i == 0 else len(f.data) - 1 for i, f in enumerate(frames)) < total_length:
            frame = self.receive_can_frame(2.0)
            if not frame:
                break

            if frame.data[0] & 0xF0 == 0x20:  # 连续帧
                seq = frame.data[0] & 0x0F
                if seq == expected_seq:
                    frames.append(frame)
                    expected_seq = (expected_seq + 1) % 16
                else:
                    self.log_message(f"序列号错误: 期望{expected_seq}, 收到{seq}")
                    break

        # 组装完整数据
        if frames:
            complete_data = frames[0].data[2:]  # 首帧数据部分
            for frame in frames[1:]:
                complete_data += frame.data[1:]  # 连续帧数据部分

            return complete_data[:total_length]

        return None

    def calculate_key_from_seed(self, seed_data):
        """使用../uds27/calc_key命令计算密钥"""
        try:
            # 将种子数据转换为十六进制字符串
            seed_hex = ''.join(f'{b:02X}' for b in seed_data)
            self.log_message(f"计算密钥，种子长度: {len(seed_data)}字节, 种子: {seed_hex}")

            # calc_key需要16字节种子，如果不足则补零
            if len(seed_data) < 16:
                padded_seed = seed_data + b'\x00' * (16 - len(seed_data))
                seed_hex = ''.join(f'{b:02X}' for b in padded_seed)
                self.log_message(f"种子补零到16字节: {seed_hex}")
            elif len(seed_data) > 16:
                # 如果超过16字节，截取前16字节
                seed_hex = ''.join(f'{b:02X}' for b in seed_data[:16])
                self.log_message(f"种子截取到16字节: {seed_hex}")

            # 调用calc_key命令
            cmd = ['../uds27/calc_key', seed_hex]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                # 解析输出，提取密钥
                output = result.stdout.strip()
                self.log_message(f"calc_key输出: {output}")

                # 查找"连续格式:"后面的密钥
                lines = output.split('\n')
                for line in lines:
                    if '连续格式:' in line:
                        # 提取冒号后面的密钥
                        key_hex = line.split('连续格式:')[1].strip()

                        # 验证是否为有效的十六进制字符串
                        if len(key_hex) == 32 and all(c in '0123456789ABCDEFabcdef' for c in key_hex):
                            key_data = bytes.fromhex(key_hex)
                            self.log_message(f"计算得到密钥: {' '.join(f'{b:02X}' for b in key_data)}")
                            return key_data
                        else:
                            self.log_message(f"提取的密钥格式无效: {key_hex}")
                            return None

                # 如果没有找到"连续格式:"，尝试查找其他格式
                self.log_message(f"未找到连续格式密钥，尝试其他解析方法")
                return None
            else:
                self.log_message(f"calc_key执行失败: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            self.log_message("calc_key执行超时")
            return None
        except Exception as e:
            self.log_message(f"调用calc_key失败: {e}")
            return None

    def step_2_security_access(self):
        """步骤2: 安全访问"""
        self.log_message("=== 步骤2: 安全访问 ===")

        # UDS 27 03 (请求种子)
        self.log_message("发送UDS 27 03 (请求种子)...")
        response = self.send_uds_request(b'\x02\x27\x03')

        # 检查是否为多帧响应
        if response and response.data[0] & 0xF0 == 0x10:
            # 发送流控帧
            flow_control = b'\x30\x00\x00\x55\x55\x55\x55\x55'
            fc_frame = CANFrame(self.request_id, flow_control)
            self.send_can_frame(fc_frame)

            # 接收连续帧
            cont_frame = self.receive_can_frame(2.0)
            if cont_frame and cont_frame.data[0] & 0xF0 == 0x20:
                # 组装完整种子数据
                seed_data = response.data[3:] + cont_frame.data[1:9]  # 16字节种子
                self.log_message(f"收到种子: {' '.join(f'{b:02X}' for b in seed_data)}")

                # 使用calc_key命令计算密钥
                key_data = self.calculate_key_from_seed(seed_data)
                if not key_data:
                    self.log_message("❌ 密钥计算失败")
                    return False

                # UDS 27 04 (发送密钥) - 多帧传输
                self.log_message("发送UDS 27 04 (发送密钥)...")

                # 首帧
                first_frame = b'\x10\x12\x27\x04' + key_data[:4]
                padded_data = first_frame + b'\x55' * (8 - len(first_frame))
                request_frame = CANFrame(self.request_id, padded_data)

                if not self.send_can_frame(request_frame):
                    return False

                # 等待ECU响应（可能是流控帧或首帧响应）
                # 由于ECU可能快速发送多帧，我们需要收集所有响应
                responses = []
                for i in range(3):  # 最多收集3个响应帧
                    resp = self.receive_can_frame(1.0)
                    if resp:
                        responses.append(resp)
                    else:
                        break

                if not responses:
                    self.log_message("未收到任何响应")
                    return False

                # 分析收到的响应
                for i, resp in enumerate(responses):
                    self.log_message(f"响应{i+1}: {' '.join(f'{b:02X}' for b in resp.data)}")

                # 查找流控帧
                flow_control_frame = None
                for resp in responses:
                    if resp.data[0] == 0x30:
                        flow_control_frame = resp
                        break

                if flow_control_frame:
                    response = flow_control_frame
                    self.log_message("找到流控帧，使用流控帧逻辑")
                else:
                    response = responses[0]  # 使用第一个响应进行主要逻辑

                if response and len(response.data) >= 1:
                    if response.data[0] == 0x30:
                        # 流控帧，继续发送连续帧
                        self.log_message("收到流控帧，发送连续帧...")

                        # 连续帧1
                        cont_frame1 = b'\x21' + key_data[4:11]
                        padded_data1 = cont_frame1 + b'\x55' * (8 - len(cont_frame1))
                        frame1 = CANFrame(self.request_id, padded_data1)
                        self.send_can_frame(frame1)

                        # 连续帧2
                        cont_frame2 = b'\x22' + key_data[11:16] + b'\x55\x55'
                        padded_data2 = cont_frame2 + b'\x55' * (8 - len(cont_frame2))
                        frame2 = CANFrame(self.request_id, padded_data2)
                        self.send_can_frame(frame2)

                        # 等待最终响应
                        final_response = self.receive_can_frame(3.0)
                        if final_response and len(final_response.data) >= 2 and final_response.data[1] == 0x67:
                            self.log_message("✅ 安全访问成功")
                            return True
                        elif final_response:
                            self.log_message(f"收到最终响应: {' '.join(f'{b:02X}' for b in final_response.data)}")

                    elif response.data[0] & 0xF0 == 0x10:
                        # 这是ECU发送的首帧响应
                        self.log_message(f"收到ECU首帧响应: {' '.join(f'{b:02X}' for b in response.data)}")

                        # 发送流控帧
                        flow_control = b'\x30\x00\x00\x55\x55\x55\x55\x55'
                        fc_frame = CANFrame(self.request_id, flow_control)
                        self.send_can_frame(fc_frame)

                        # 接收连续帧
                        cont_response = self.receive_can_frame(2.0)
                        if cont_response:
                            self.log_message(f"收到ECU连续帧: {' '.join(f'{b:02X}' for b in cont_response.data)}")

                            # 检查是否为负响应
                            if len(response.data) >= 3 and response.data[2] == 0x7F:
                                nrc = cont_response.data[1] if len(cont_response.data) > 1 else 0x00
                                self.log_message(f"收到负响应 NRC=0x{nrc:02X}")
                            elif len(response.data) >= 3 and response.data[2] == 0x67:
                                self.log_message("✅ 安全访问成功")
                                return True

                    elif response.data[0] & 0xF0 == 0x20:
                        # 这是一个连续帧，说明我们遗漏了首帧
                        self.log_message(f"收到连续帧（遗漏首帧）: {' '.join(f'{b:02X}' for b in response.data)}")

                    elif len(response.data) >= 2 and response.data[1] == 0x67:
                        # 单帧正响应
                        self.log_message("✅ 安全访问成功")
                        return True

                    elif len(response.data) >= 3 and response.data[1] == 0x7F:
                        # 单帧负响应
                        nrc = response.data[3] if len(response.data) > 3 else 0x00
                        self.log_message(f"收到负响应 NRC=0x{nrc:02X}")

                    else:
                        # 其他类型的响应
                        self.log_message(f"收到意外响应: {' '.join(f'{b:02X}' for b in response.data)}")

                self.log_message("❌ 安全访问失败")
                return False

        elif response and len(response.data) >= 2 and response.data[1] == 0x67:
            # 单帧响应
            seed_data = response.data[3:19]  # 16字节种子
            self.log_message(f"收到种子: {' '.join(f'{b:02X}' for b in seed_data)}")
            # ... 处理单帧种子响应的逻辑

        self.log_message("❌ 请求种子失败")
        return False
    
    def step_3_write_fingerprint(self):
        """步骤3: 写入指纹"""
        self.log_message("=== 步骤3: 写入指纹 ===")
        
        # UDS 2E F1 84 (写数据标识符) - 多帧传输
        fingerprint_data = bytes([0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08])
        
        # 首帧
        first_frame = b'\x10\x0C\x2E\xF1\x84' + fingerprint_data[:3]
        response = self.send_uds_request(first_frame)
        
        if response and len(response.data) >= 1 and response.data[0] == 0x30:
            # 连续帧
            cont_frame = b'\x21' + fingerprint_data[3:] + b'\x55'
            response = self.send_uds_request(cont_frame)
            
            if response and len(response.data) >= 3 and response.data[1] == 0x6E:
                self.log_message("✅ 写入指纹成功")
                return True
        
        self.log_message("❌ 写入指纹失败")
        return False
    
    def step_4_routine_control(self):
        """步骤4: 例程控制"""
        self.log_message("=== 步骤4: 例程控制 ===")
        
        # UDS 31 01 FF 00 (启动例程) - 多帧传输
        routine_data = bytes([0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00, 0x52, 0x00])
        
        # 首帧
        first_frame = b'\x10\x0D\x31\x01\xFF\x00' + routine_data[:2]
        response = self.send_uds_request(first_frame)
        
        if response and len(response.data) >= 1 and response.data[0] == 0x30:
            # 连续帧
            cont_frame = b'\x21' + routine_data[2:] + b'\x55\x55'
            response = self.send_uds_request(cont_frame)
            
            if response and len(response.data) >= 4 and response.data[1] == 0x71:
                self.log_message("✅ 例程控制成功")
                return True
        
        self.log_message("❌ 例程控制失败")
        return False
    
    def step_5_request_download(self, block_num):
        """步骤5: 请求下载"""
        self.log_message(f"=== 步骤5: 请求下载 Block {block_num} ===")
        
        # UDS 34 00 44 (请求下载) - 多帧传输
        # 地址和长度根据block调整
        if block_num == 1:
            address = 0x00400020
            length = 0x00004400
        elif block_num == 2:
            address = 0x00404400
            length = 0x00004400
        else:  # block 3
            address = 0x00408800
            length = 0x00004400
        
        download_data = struct.pack('>I', address) + struct.pack('>I', length)
        
        # 首帧
        first_frame = b'\x10\x0B\x34\x00\x44' + download_data[:3]
        response = self.send_uds_request(first_frame)
        
        if response and len(response.data) >= 1 and response.data[0] == 0x30:
            # 连续帧
            cont_frame = b'\x21' + download_data[3:] + b'\x55\x55'
            response = self.send_uds_request(cont_frame)
            
            if response and len(response.data) >= 3 and response.data[1] == 0x74:
                max_block_length = (response.data[2] << 8) | response.data[3]
                self.log_message(f"✅ 请求下载成功, 最大块长度: {max_block_length}")
                return True
        
        self.log_message("❌ 请求下载失败")
        return False
    
    def step_6_transfer_data(self, block_num):
        """步骤6: 传输数据"""
        self.log_message(f"=== 步骤6: 传输数据 Block {block_num} ===")
        
        # 读取hex文件数据
        hex_data = self.read_hex_file_block(block_num)
        if not hex_data:
            self.log_message("❌ 读取hex文件失败")
            return False
        
        # 分块传输数据
        block_size = 0x100  # 256字节每块
        sequence = 1
        
        for i in range(0, len(hex_data), block_size):
            chunk = hex_data[i:i+block_size]
            
            # UDS 36 XX (传输数据)
            if not self.transfer_data_chunk(sequence, chunk):
                self.log_message(f"❌ 传输数据块 {sequence} 失败")
                return False
            
            sequence += 1
            if sequence > 255:
                sequence = 0
        
        self.log_message(f"✅ Block {block_num} 数据传输完成")
        return True
    
    def transfer_data_chunk(self, sequence, data):
        """传输单个数据块"""
        # 构造多帧传输
        total_length = len(data) + 2  # 服务ID + 序列号 + 数据
        
        if total_length <= 6:  # 单帧
            frame_data = bytes([total_length, 0x36, sequence]) + data
            response = self.send_uds_request(frame_data)
        else:  # 多帧
            # 首帧
            first_frame = bytes([0x10 | ((total_length >> 8) & 0x0F), total_length & 0xFF, 0x36, sequence]) + data[:4]
            response = self.send_uds_request(first_frame)
            
            if response and response.data[0] == 0x30:
                # 连续帧
                remaining_data = data[4:]
                frame_seq = 1
                
                while remaining_data:
                    chunk = remaining_data[:7]
                    remaining_data = remaining_data[7:]
                    
                    cont_frame = bytes([0x20 | frame_seq]) + chunk + b'\x55' * (7 - len(chunk))
                    if remaining_data:  # 不是最后一帧
                        self.send_uds_request(cont_frame)
                    else:  # 最后一帧
                        response = self.send_uds_request(cont_frame)
                    
                    frame_seq += 1
                    if frame_seq > 15:
                        frame_seq = 0
        
        # 检查响应
        if response and len(response.data) >= 2 and response.data[1] == 0x76:
            return True
        else:
            return False
    
    def read_hex_file_block(self, block_num):
        """读取hex文件中指定block的数据"""
        try:
            with open('kaiwo.hex', 'r') as f:
                lines = f.readlines()
            
            # 简化处理：每个block大约220行hex数据
            start_line = (block_num - 1) * 220
            end_line = min(start_line + 220, len(lines))
            
            block_data = b''
            for line in lines[start_line:end_line]:
                line = line.strip()
                if line.startswith(':'):
                    # 解析Intel HEX格式
                    byte_count = int(line[1:3], 16)
                    data_part = line[9:9+byte_count*2]
                    block_data += bytes.fromhex(data_part)
            
            self.log_message(f"读取Block {block_num}数据: {len(block_data)} 字节")
            return block_data
            
        except Exception as e:
            self.log_message(f"读取hex文件失败: {e}")
            return None
    
    def step_7_request_transfer_exit(self):
        """步骤7: 请求传输退出"""
        self.log_message("=== 步骤7: 请求传输退出 ===")
        
        # UDS 37 (请求传输退出)
        response = self.send_uds_request(b'\x01\x37')
        
        if response and len(response.data) >= 2 and response.data[1] == 0x77:
            self.log_message("✅ 传输退出成功")
            return True
        else:
            self.log_message("❌ 传输退出失败")
            return False
    
    def flash_single_block(self, block_num):
        """刷写单个block"""
        self.log_message(f"\n{'='*20} 开始刷写 Block {block_num} {'='*20}")
        
        # 步骤5: 请求下载
        if not self.step_5_request_download(block_num):
            return False
        
        time.sleep(0.1)
        
        # 步骤6: 传输数据
        if not self.step_6_transfer_data(block_num):
            return False
        
        time.sleep(0.1)
        
        # 步骤7: 请求传输退出
        if not self.step_7_request_transfer_exit():
            return False
        
        self.log_message(f"✅ Block {block_num} 刷写完成")
        return True
    
    def run_flash_sequence(self):
        """运行完整的刷写序列"""
        self.log_message("开始UDS刷写序列...")
        
        # 步骤1: 诊断会话控制
        if not self.step_1_session_control():
            return False
        
        time.sleep(0.5)
        
        # 步骤2: 安全访问
        if not self.step_2_security_access():
            return False
        
        time.sleep(0.5)
        
        # 步骤3: 写入指纹
        if not self.step_3_write_fingerprint():
            return False
        
        time.sleep(0.5)
        
        # 步骤4: 例程控制
        if not self.step_4_routine_control():
            return False
        
        time.sleep(0.5)
        
        # 刷写三个blocks
        for block_num in range(1, 4):
            if not self.flash_single_block(block_num):
                self.log_message(f"❌ Block {block_num} 刷写失败")
                return False
            
            self.blocks_completed += 1
            time.sleep(1.0)  # block间延时
        
        self.log_message("🎉 所有三个Block刷写完成!")
        return True
    
    def cleanup(self):
        """清理资源"""
        if self.socket:
            self.socket.close()
        if self.log_file:
            self.log_file.close()
    
    def run(self):
        """运行刷写程序"""
        print("UDS刷写工具")
        print("=" * 16)
        print(f"请求CAN ID: 0x{self.request_id:03X}")
        print(f"响应CAN ID: 0x{self.response_id:03X}")
        print(f"CAN接口: {self.interface}")
        print("将刷写3个blocks")
        
        if not self.init_can_socket():
            return False
        
        if not self.open_log_file():
            return False
        
        try:
            return self.run_flash_sequence()
        except KeyboardInterrupt:
            print("\n用户中断刷写")
            return False
        except Exception as e:
            print(f"\n刷写过程中发生错误: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("用法: python3 hex_uds_flash.py [请求CAN_ID] [响应CAN_ID] [接口]")
        print("默认: python3 hex_uds_flash.py 715 795 can0")
        sys.exit(0)
    
    # 解析参数
    request_id = int(sys.argv[1], 16) if len(sys.argv) > 1 else 0x715
    response_id = int(sys.argv[2], 16) if len(sys.argv) > 2 else 0x795
    interface = sys.argv[3] if len(sys.argv) > 3 else 'can0'
    
    flasher = UDSFlasher(request_id, response_id, interface)
    
    try:
        success = flasher.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
